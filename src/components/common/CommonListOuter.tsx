import React, { useEffect, useRef, useState } from "react"
import commonOuterStyle from '@/styles/CommonListOuter.module.scss'
import { Button, Dropdown, Input, Space, Tooltip, Popover } from 'antd'
import type { MenuProps } from 'antd';
import { useRouter } from "next/router";

import FlowListIndex from '@/components/flow3.0/FlowListIndex'

import searchIcon from '@/images/knowledge_index_search.svg'
import commonSelectIcon from '@/images/commonList/common-select.svg'
import * as constants from "@/constants/appConstants";
import { reqGetChannelList } from "@/service/agent";
import headerBackBtn from "@/images/agent-header-back-icon.png";
import channelLists from "@/images/channel-list.svg";
import channelCards from "@/images/channel-cards.svg";
import arrowDown from '@/images/commonList/arrow-down.svg'
import { getCurrentUrlParamValue } from "@/utils/url";
import createNewIcon from '@/images/agent-create-new.svg';


const CommonListOuter = (props: any) => {

    const { pageName, children, tabChangeKeyHandle, queryTab } = props
    const router = useRouter();
    const chainId = router.query.id;
    const teamId = router.query.teamId;
    const agentId = router.query.agentId;
    const query = router.query.from;

    const [isWaiWang, setIsWaiWang] = useState(false);
    const [sortTabKey, setSortTabKey] = useState(pageName === 'template' ? '2' : '1')
    const [selectPropel, setSelectPropel] = useState(0)
    const [selectStatus, setSelectStatus] = useState(pageName === 'knowledge' ? '' : '0')
    const [selectTabKey, setSelectTabKey] = useState(pageName === 'template' ? (router.query?.templateTab || 'agents') : pageName === 'knowledge' ? 'knowledge-data' : 'prompt-text')
    const [createNewBtnTitle, setCreateNewBtnTitle] = useState('创建智能体')
    const [pageTitle, setPageTitle] = useState('智能体')
    const [propelList, setPropelList] = useState<any[]>([])
    const agentChildRef = useRef();
    const flowChildRef = useRef();
    const apiChildRef = useRef();
    const knowledgeChildRef = useRef();
    const promptChildRef = useRef();
    const templateChildRef = useRef();
    const [agentPublishedChannelId, setAgentPublishedChannelId] = useState<any>('') // 智能体发布渠道id
    const [channelList, setChannelList] = useState<any[]>([])

    const [bgChannelList, setBgChannelList] = useState(false) // 管理端-渠道商店列表形式展示

    // llmops 平台
    const [isFromLlmopsBoard, setIsFromLlmopsBoard] = useState(false);
    const [isAgentListForPanBoard, setIsAgentListForPanBoard] = useState(false); // 云盘智能体列表页
    const [isOtherListForPanBoard, setIsOtherListForPanBoard] = useState(false);// 云盘除了AgentList外的其他一级列表页
    const [isYunPanBoard, setIsYunPanBoard] = useState(false); // 云盘
    const [moreAgentTeams, setMoreAgentTeams] = useState(false);

    useEffect(() => {
        const res = localStorage.getItem(constants.prompt_isLlmopsBoard);
        if (res && res === 'true') {
            setIsFromLlmopsBoard(true);
        }
        const isFromYunPan = (getCurrentUrlParamValue('source') === 'yunpanBoard' || localStorage.getItem('prompt_isYunPanBoard') === 'true') && window.location.pathname.indexOf('/agentList') > -1;
        if (isFromYunPan) {
            setIsAgentListForPanBoard(true);
        }

        const isFromOtherYunPan = (getCurrentUrlParamValue('source') === 'yunpanBoard' || localStorage.getItem('prompt_isYunPanBoard') === 'true')
            && (window.location.pathname.indexOf('/templateList') > -1
                || window.location.pathname.indexOf('/flowList') > -1
                || window.location.pathname.indexOf('/toolList') > -1
            );
        if (isFromOtherYunPan) {
            setIsOtherListForPanBoard(true);
        }

        const hostname = window.location.hostname;
        if (['agent.qihoo.net', 'localhost'].some(hosts => hostname.indexOf(hosts) > -1)) {
            setIsWaiWang(false);
        } else {
            setIsWaiWang(true)
        }

        const isYunPan = localStorage.getItem('prompt_isYunPanBoard') === 'true' || router.query.source === 'yunpanBoard';
        setIsYunPanBoard(isYunPan);
    }, [])

    useEffect(() => {
        if (pageName === 'knowledge' && selectTabKey === 'knowledge-data') {
            setCreateNewBtnTitle('创建知识数据')
            setPageTitle('知识库')
        } else if (pageName === 'knowledge' && selectTabKey === 'knowledge-chat') {
            setCreateNewBtnTitle('创建知识问答')
            setPageTitle('知识库')
        } else if (pageName === 'knowledge' && selectTabKey === 'knowledge-external') {
            setCreateNewBtnTitle('连接外部知识库')
            setPageTitle('知识库')
        } else if (pageName === 'prompt' && selectTabKey === 'prompt-text') {
            setCreateNewBtnTitle('创建文生文')
            setPageTitle('提示词')
        } else if (pageName === 'prompt' && selectTabKey === 'prompt-image') {
            setCreateNewBtnTitle('创建文生图')
            setPageTitle('提示词')
        } else if (pageName === 'agents') {
            setCreateNewBtnTitle('创建智能体')
            setPageTitle('智能体')
        } else if (pageName === 'flow') {
            setCreateNewBtnTitle('+ 创建多智能体蜂群')
            setPageTitle('多智能体蜂群')
        } else if (pageName === 'api') {
            setCreateNewBtnTitle('创建插件')
            setPageTitle('插件')
        } else if (pageName === 'template') {
            if (queryTab === 'api') {
                setPageTitle('插件商店')
            } else {
                setPageTitle('智能体商店')
            }
        }
    }, [pageName, selectTabKey, queryTab])


    useEffect(() => {
        const propelList = [
            { key: 0, label: isYunPanBoard ? '全部创建人' : '全部' },
            { key: localStorage.getItem(constants.prompt_userId) || 0, label: '我自己' }
        ]
        setPropelList(propelList)
    }, [isYunPanBoard])

    useEffect(() => {
        // 处理根据路由，变更激活的tab
        // 提示词，知识库
        if (
            router.query?.tab === 'prompt-text' ||
            router.query?.tab === 'prompt-image' ||
            router.query?.tab === 'knowledge-data' ||
            router.query?.tab === 'knowledge-chat'
        ) {
            setSelectTabKey(router.query?.tab as string)
        }
        // 智能体商店 插件
        if (
            router.query?.templateTab === 'agents' ||
            router.query?.templateTab === 'api'
        ) {
            setSelectTabKey(router.query?.templateTab as string)
        }
        setAgentPublishedChannelId(router.query?.publishedChannel || '')
    }, [router.query])

    useEffect(() => {
        const fetchData = async () => {
            const res = await reqGetChannelList()
            setChannelList([...res])
        };
        if (agentPublishedChannelId.length && pageName === 'template') {
            fetchData()
        }
    }, [agentPublishedChannelId, pageName])

    const statusList = pageName === 'agents' ?
        [
            { key: '0', label: isYunPanBoard ? '全部状态' : '全部' },
            { key: '1', label: '未发布' },
            { key: '2', label: '已发布' },
            { key: '3', label: '已公开' },
        ]
        : [
            { key: '0', label: isYunPanBoard ? '全部状态' : '全部' },
            { key: '1', label: '未部署' },
            { key: '2', label: '已部署' },
        ]

    const tabItemHandle = (key: string) => {
        setSelectTabKey(key)
        router.push(`${router.pathname}?teamId=${router.query.teamId}&tab=${key}`);
    }

    const sortTabItemHandle = async (key: string) => {
        await router.push(`${router.pathname}?teamId=${router.query.teamId}&tab=${key}&templateTab=${router.query.templateTab}`);
        setSortTabKey(key)
    }

    const templateTabItems = [
        {
            key: '1',
            label: '最新',
        },
        {
            key: '2',
            label: '最热',
        }
    ]

    const promptTab = () => {
        const knowledgeTabItems = [
            {
                key: 'knowledge-data',
                label: '知识数据',
            },
            {
                key: 'knowledge-chat',
                label: '知识问答',
            },
            {
                key: 'knowledge-external',
                label: '外部知识库',
            }
        ]

        const promptTabItems = [
            {
                key: 'prompt-text',
                label: '文生文',
            },
            {
                key: 'prompt-image',
                label: '文生图',
            }
        ]

        const discoverTabItems = [
            {
                key: 'agents',
                label: '智能体'
            },
            {
                key: 'api',
                label: '插件'
            }
        ]

        if (pageName === 'knowledge') {
            return (
                <>
                    <div className={commonOuterStyle.tab}>
                        {
                            knowledgeTabItems.map(item => {
                                return <div onClick={() => { tabItemHandle(item.key) }} className={commonOuterStyle.tabItem + ' ' + (selectTabKey === item.key ? commonOuterStyle.tabItemActive : '')} key={item.key}>{item.label}</div>
                            })
                        }
                    </div>
                </>
            )
        } else if (pageName === 'prompt') {
            return (
                <>
                    <div className={commonOuterStyle.tab}>
                        {
                            promptTabItems.map(item => {
                                return <div onClick={() => { tabItemHandle(item.key) }} className={commonOuterStyle.tabItem + ' ' + (selectTabKey === item.key ? commonOuterStyle.tabItemActive : '')} key={item.key}>{item.label}</div>
                            })
                        }
                    </div>
                </>
            )
        } else if (pageName === 'template') {
            return (
                <>
                    <div className={commonOuterStyle.tab + ' ' + commonOuterStyle.templateTab}>
                        {
                            discoverTabItems.map(item => {
                                return <div onClick={() => { tabItemHandle(item.key) }} className={commonOuterStyle.tabItem + ' ' + (selectTabKey === item.key ? commonOuterStyle.tabItemActive : '')} key={item.key}>{item.label}</div>
                            })
                        }
                    </div>
                </>
            )
        }
    }

    const changeStatusHandle = (key: string) => {
        setSelectStatus(key)
        pageName === 'agents' && (agentChildRef.current as any)?.changeStatusHandle(key)
        pageName === 'flow' && (flowChildRef.current as any)?.changeStatusHandle(key)
        pageName === 'api' && (apiChildRef.current as any)?.changeStatusHandle(key)
        pageName === 'prompt' && (promptChildRef.current as any)?.changeStatusHandle(key)
    }

    const changePropelHandle = (key: number) => {
        setSelectPropel(key)
        pageName === 'agents' && (agentChildRef.current as any)?.changePropelHandle(key)
        pageName === 'flow' && (flowChildRef.current as any)?.changePropelHandle(key)
        pageName === 'api' && (apiChildRef.current as any)?.changePropelHandle(key)
        pageName === 'knowledge' && (knowledgeChildRef.current as any)?.changePropelHandle(key)
        pageName === 'prompt' && (promptChildRef.current as any)?.changePropelHandle(key)
    }
    const getItems = (key: number) => {

        if (key === 1) {
            const items: MenuProps['items'] = propelList.map((item, index) => {
                return {
                    key: item.key,
                    label: (
                        <div className={commonOuterStyle.selectProItem} onClick={() => { changePropelHandle(item.key) }}>
                            <span className={commonOuterStyle.selectProItemTitle}>{item.label}</span>
                            {selectPropel === item.key ? <img className={commonOuterStyle.selectProItemTrue} src={commonSelectIcon.src} alt="" /> : null}
                        </div>
                    ),
                }
            })
            return { items }
        } else {
            const items: MenuProps['items'] = statusList.map((item, index) => {
                return {
                    key: item.key,
                    label: (
                        <div className={commonOuterStyle.selectProItem} onClick={() => { changeStatusHandle(item.key) }}>
                            <span className={commonOuterStyle.selectProItemTitle}>{item.label}</span>
                            {selectStatus === item.key ? <img className={commonOuterStyle.selectProItemTrue} src={commonSelectIcon.src} alt="" /> : null}
                        </div>
                    ),
                }
            })
            return { items }
        }
    }

    const createApiHandle = (type: number) => {
        (apiChildRef.current as any)?.createNewHandel(type)
    }

    const createNewHandel = (type: any = '') => {
        if (type && type == 'moreAgentTeams' && pageName === 'flow') {
            (flowChildRef.current as any)?.createNewHandel()
            return;
        }
        pageName === 'agents' && (agentChildRef.current as any)?.createNewHandel()
        pageName === 'flow' && (flowChildRef.current as any)?.createNewHandel()
        pageName === 'knowledge' && (knowledgeChildRef.current as any)?.createNewHandel()
        pageName === 'prompt' && (promptChildRef.current as any)?.createNewHandel()
    }

    const searchDoneHandle = (e: any) => {
        pageName === 'agents' && (agentChildRef.current as any)?.searchDoneHandle(e.target.value)
        pageName === 'flow' && (flowChildRef.current as any)?.searchDoneHandle(e.target.value)
        pageName === 'api' && (apiChildRef.current as any)?.searchDoneHandle(e.target.value)
        pageName === 'knowledge' && (knowledgeChildRef.current as any)?.searchDoneHandle(e.target.value)
        pageName === 'prompt' && (promptChildRef.current as any)?.searchDoneHandle(e.target.value)
        pageName === 'template' && (templateChildRef.current as any)?.searchDoneHandle(e.target.value)
    }

    const onInputChangeHandle = (e: any) => {
        if (e.target.value === '') {
            pageName === 'agents' && (agentChildRef.current as any)?.searchDoneHandle(e.target.value)
            pageName === 'flow' && (flowChildRef.current as any)?.searchDoneHandle(e.target.value)
            pageName === 'api' && (apiChildRef.current as any)?.searchDoneHandle(e.target.value)
            pageName === 'knowledge' && (knowledgeChildRef.current as any)?.searchDoneHandle(e.target.value)
            pageName === 'prompt' && (promptChildRef.current as any)?.searchDoneHandle(e.target.value)
            pageName === 'template' && (templateChildRef.current as any)?.searchDoneHandle(e.target.value)
        }
    }

    const pageToAgentMarket = () => {
        router.push('/templateList?templateTab=agents&publishedChannel=16&identifier=HzGk');
    }

    // 当前渠道渲染
    const CurrentChannel = () => {
        if (!agentPublishedChannelId.length || pageName !== 'template' || !channelList.length) return null
        return <div className={commonOuterStyle.headerLeftChannel}>
            {
                channelList.map((item: any) => {
                    if (item.id == agentPublishedChannelId) {
                        return <React.Fragment key={item.id}>
                            <>
                                <img className={commonOuterStyle.channelIcon} src={item.image_url} alt="" />
                                <div className={commonOuterStyle.channelName}>{item.channel_name}</div>
                            </>
                        </React.Fragment>
                    }

                })
            }
        </div>
    }

    const handleBack = () => {
        router.back()
    }

    const handlePublishBack = () => {
        router.replace(`/templateList?teamId=${teamId}&templateTab=agents`);
    }

    const changeFormat = () => {
        setBgChannelList(!bgChannelList)
    }

    const createSpecialHandel = (type: string) => {
        apiChildRef.current && (apiChildRef.current as any)?.createSpecialHandel(type);
    }

    const idePopContent = () => {
        return (
            <div className={commonOuterStyle.disableIde}>功能升级中，敬请期待。</div>
        )
    }

    const apiContent = (
        <div className={commonOuterStyle.apiContent}>
            {!isWaiWang && <Popover content={idePopContent}>
                <div
                    className={commonOuterStyle.contentFirstTab}
                // onClick={() => createApiHandle(1)}
                >
                    从IDE中创建<div className={commonOuterStyle.versionTest}>测试版</div>
                </div>
            </Popover>}
            <div className={commonOuterStyle.contentTab} onClick={() => createApiHandle(0)}>基于已有服务创建</div>
            <div className={commonOuterStyle.contentTab} onClick={() => createSpecialHandel('json')}>Json格式创建</div>
            <div className={commonOuterStyle.contentTab} onClick={() => createSpecialHandel('yaml')}>Yaml格式创建</div>
            <div className={commonOuterStyle.contentTab} onClick={() => createApiHandle(2)}>数据库连接</div>
        </div>
    )

    return (
        <div className={commonOuterStyle.outerContent}>
            <div className={commonOuterStyle.header}>
                <div className={commonOuterStyle.headerLeft}>
                    {
                        (agentPublishedChannelId.length && router.pathname.indexOf('/background/agentBgPublishChannel') > -1) ?
                            <img
                                src={headerBackBtn.src}
                                width="20px"
                                style={{ "marginRight": '28px' }}
                                onClick={handleBack}
                            />
                            : null
                    }

                    {!isFromLlmopsBoard && <div className={commonOuterStyle.headerLeftTitle}
                        style={{
                            fontSize: isAgentListForPanBoard || isOtherListForPanBoard ? '18px' : '24px',
                            lineHeight: isAgentListForPanBoard || isOtherListForPanBoard ? '24px' : '32px',
                            fontWeight: isAgentListForPanBoard || isOtherListForPanBoard ? '500' : '600'
                        }}>
                        {pageTitle}
                    </div>}
                    <>{CurrentChannel()}</>
                    {/* <div onClick={pageToAgentMarket}>
                        发布渠道跳转智能体
                    </div> */}
                    <div className={commonOuterStyle.headerLeftTab}>
                        {promptTab()}
                    </div>
                </div>
                <div className={commonOuterStyle.headerRight}>
                    {
                        pageName != 'template' ? (
                            <>
                                <div className={isYunPanBoard ? commonOuterStyle.createPropelForYunPan : commonOuterStyle.createPropel}>
                                    <Dropdown menu={getItems(1)} placement="bottom" overlayClassName={commonOuterStyle.dropMenuBox}>
                                    <Button>
                                        <Space className={isYunPanBoard ? commonOuterStyle.spaceForYunPan : ''}>
                                            {!isYunPanBoard && <span className={commonOuterStyle.selectTitle}>创建人:</span>}
                                            <span className={commonOuterStyle.selectSubtitle}>{propelList.find(item => item.key === selectPropel)?.label}</span>
                                            <img src={arrowDown.src} height={18} width={18} style={{ marginTop: '6px' }} />
                                        </Space>
                                        </Button>
                                    </Dropdown>
                                </div>
                                {pageName != 'knowledge' && <div className={isYunPanBoard ? commonOuterStyle.statusForYunPan : commonOuterStyle.status}>
                                    <Dropdown menu={getItems(2)} overlayClassName={commonOuterStyle.dropMenuBox}>
                                    <Button>
                                        <Space className={isYunPanBoard ? commonOuterStyle.spaceForYunPan : ''}>
                                            {!isYunPanBoard && <span className={commonOuterStyle.selectTitle}>状态:</span>}
                                            <span className={commonOuterStyle.selectSubtitle}>{statusList.find(item => item.key === selectStatus)?.label}</span>
                                            <img src={arrowDown.src} height={18} width={18} style={{ marginTop: '6px' }} />
                                        </Space>
                                        </Button>
                                    </Dropdown>
                                </div>}
                            </>
                        ) :
                            <>
                                {
                                    router.query?.publishedChannel ? null :
                                        <div className={isOtherListForPanBoard ? commonOuterStyle.templateTabForYunPan : commonOuterStyle.templateTab} style={{ marginRight: isOtherListForPanBoard ? '24px' : '16px' }}>
                                            {
                                                templateTabItems.map(item => {
                                                    return <div onClick={() => { sortTabItemHandle(item.key) }}
                                                        className={isOtherListForPanBoard ?
                                                            (commonOuterStyle.templateTabItemForYunPan + ' ' + (sortTabKey === item.key ? commonOuterStyle.templateTabItemActiveForYunPan : ''))
                                                            : commonOuterStyle.templateTabItem + ' ' + (sortTabKey === item.key ? commonOuterStyle.templateTabItemActive : '')} key={item.key}>{item.label}</div>
                                                })
                                            }
                                        </div>
                                }
                            </>
                    }

                    {
                        (agentPublishedChannelId.length && router.pathname.indexOf('/background/agentBgPublishChannel') > -1) ?
                            <Tooltip title={bgChannelList ? '卡片形式' : '列表形式'} placement="left">
                                <div className={commonOuterStyle.channelFormat} onClick={changeFormat} style={{ cursor: 'pointer' }}>
                                    <img src={bgChannelList ? channelCards.src : channelLists.src} />
                                </div>
                            </Tooltip>

                            :
                            null
                    }

                    <div className={commonOuterStyle.search}>
                        <Input onPressEnter={(e) => { searchDoneHandle(e) }} onChange={(e) => { onInputChangeHandle(e) }} prefix={<img src={searchIcon.src} alt="" />}
                            placeholder="请输入关键词搜索" allowClear
                            style={{ borderColor: '#E1E7ED', width: isYunPanBoard ? '276px' : '256px', borderRadius: isYunPanBoard ? '4px' : '8px' }} key={router.query.templateTab + '' || ''} />
                    </div>
                    {(pageName != 'template' && pageName != 'api') && (isYunPanBoard ?
                        <Button type="primary" className={commonOuterStyle.btn}
                            style={{ background: '#006BFF', borderRadius: '4px', marginLeft: '24px' }}
                            onClick={createNewHandel}>
                            <img src={createNewIcon.src}
                                style={{ width: '20px', marginRight: '-5px' }} />{createNewBtnTitle}</Button> : <>
                        <Button type="primary" className={commonOuterStyle.btn} style={{ background: '#1D7CFF', borderRadius: '8px' }} onClick={() => {
                            setMoreAgentTeams(false);
                            createNewHandel();
                        }}>{createNewBtnTitle}</Button>
                        {/* <Button type="primary" className={commonOuterStyle.btn} style={{ background: '#1D7CFF', borderRadius: '8px' }} onClick={() => {
                            setMoreAgentTeams(true);
                            createNewHandel('moreAgentTeams');
                        }}>创建多智能体组队</Button> */}
                        </>)}
                    {pageName === 'api' && <Popover placement="bottomRight" arrow={false} content={apiContent}>
                        {
                            isYunPanBoard ? <Button type="primary" className={commonOuterStyle.btn}
                                style={{ background: '#006BFF', borderRadius: '4px', marginLeft: '24px' }}><img src={createNewIcon.src}
                                    style={{ width: '20px', marginRight: '-5px' }} />{createNewBtnTitle}</Button> :
                                <Button type="primary" className={commonOuterStyle.btn} style={{ background: '#1D7CFF', borderRadius: '8px' }}>{createNewBtnTitle}</Button>
                        }
                    </Popover>}
                </div>
            </div >
            <div className={commonOuterStyle.mainList}>
                {pageName === 'flow' && <FlowListIndex ref={flowChildRef} moreAgentTeams={moreAgentTeams} setMoreAgentTeams={setMoreAgentTeams}/>}
            </div>
        </div >
    )
}

export default CommonListOuter