declare const window: any;
declare const parent: any;

import React, { useEffect, useState } from "react";

import { Select, Popover, message, Tooltip, Dropdown, Space } from "antd";

import Router from "next/router";
import { useRouter } from "next/router";

import menuStyles from "@/styles/Menu.module.scss";
import {
  reqLogout,
  reqLogoutWaiWang,
} from "@/service/login";
import * as R from 'ramda';

import { reqProjectSelectList, reqUserInfo, reqIsWhiteList } from "@/service/common";
import { routerList, systemSuperManagerRouters, systemManagerRouters, templateRouterList, IsNotAgentsystemSuperManagerRouters, IsNotAgentsystemManagerRouters } from "./router";

import FeedbackModal from "@/components/common/FeedbackModal";
import CreateNewProjectModal from "@/components/common/CreateNewProjectModal";
import { isNullOrEmpty } from "@/utils/common";
import { waiWangHosts } from '@/config/commonConfig'

import logo from "@/images/menu/sea-logo.svg";
import agent<PERSON>ogo from "@/images/menu/sea-factory-logo.svg";

import user from "@/images/menu/user.png";
import backgroundManageIcon from "@/images/menu/backgroundManage.png";
import logoutIcon from "@/images/menu/logout.png";
import doubleArrowLeft from "@/images/menu/doubleArrowLeft.png";
import doubleArrowRight from "@/images/menu/doubleArrowRight.png";

import * as constants from "@/constants/appConstants";

import teamProject from "@/images/menu/team-project.svg";
import selfProject from "@/images/menu/self-project.svg";
import fedIcon from "@/images/menu/menu-fed.svg";
import docIcon from "@/images/menu/menu-doc.svg";
import dropdownIcon from "@/images/menu/menu_dropdown_arrow.svg";
import commonSelectIcon from '@/images/commonList/common-select.svg'
import menuAddIcon from '@/images/menu/menu_add_icon.svg'
import isc from "@/images/isc.png";
import type { MenuProps } from 'antd';
import { goToLoginWithClientUserInfo, goToLoginWithCompanyBrainToken, goToLoginWithYunPanToken, goClientUserInfoCommon } from "@/utils/tuituiLogin.js";
import MenuAgentHelper from "@/images/menu/menu_agentHelper.svg"
import menuAgentHelperSelect from "@/images/menu/menu_agentHelper_select.svg"
import homeDefault from "@/images/menu/home-default.svg"
import homeActivate from "@/images/menu/home-activate.svg"
import { getCurrentUrlParamValue, getCurrentUrlParamParse } from "@/utils/url";
import useCurrentProjectRole from "@/hooks/useCurrentProjectRole";

type Callback = () => void;

export default function Menu(prop: any) {
  const { isBackground, isFullScreen, onCompanyBrainValue } = prop;
  const { updateRole } = useCurrentProjectRole();

  const router = useRouter();
  const teamId = router.query.teamId;
  const querySrc = router.query.src;
  const [projectList, setProjectList] = useState(new Array());// 项目列表
  const [currentProject, setCurrentProject] = useState<any>({});// 当前选择的项目
  const [isMobile, setIsMobile] = useState(false); // 是否是移动端

  const [userInfo, setUserInfo] = useState({
    user_id: 0,
    username: "",
    email: "",
    phone_number: "",
    // 系统角色，0：无系统权限，1:系统超级管理员 2:系统管理员
    system_role: 0,
    src: "",
  });
  const [isOuterNet, setIsOuterNet] = useState(false);
  const [isZhiNao, setIsZhiNao] = useState(false);
  const [showFold, setShowFold] = useState(false);
  const [isFold, setIsFold] = useState(false);
  const [selectName, setSelectName] = useState("template");
  const [mouserEnterName, setMouserEnterName] = useState("");
  const [openFeedbackModal, setOpenFeedbackModal] = useState(false);
  const [showCreateProjectModal, setShowCreateProjectModal] = useState(false);
  const [backGroundRouterList, setBackGroundRouterList] = useState(new Array())
  const [isFromTuituiBoard, setIsFromTuituiBoard] = useState(false);
  const [isFromZhiYuBoard, setIsFromZhiYuBoard] = useState(false);
  const [isFromYunPanBoard, setIsFromYunPanBoard] = useState(false);
  const [isFromCompanyBrainBoard, setIsFromCompanyBrainBoard] = useState(false);
  const [isSelectAgentHelper, setIsSelectAgentHelper] = useState<boolean>(router.pathname.includes('agentCreate'))
  const [isReloadLoginBySearch, setIsReloadLoginBySearch] = useState(true);
  const [isFromLlmopsBoard, setIsFromLlmopsBoard] = useState(false); // 是否来自llmopsBoard平台
  const [waibuProjectId, setWaibuProjectId] = useState(''); // llmops项目id   云盘也需要处理成和llmpos一样的逻辑
  const [platformList, setPlatformList] = useState(constants.prompt_platformList);
  const [platformValue, setPlatformValue] = useState('');
  const [isVisibleLoginOut, setIsVisibleLoginOut] = useState(true);

  useEffect(() => {
    const userName = getLocalStorageItem(constants.prompt_userName);
    const systemRole = getLocalStorageItem(constants.prompt_systemRole);
    const isFoldLocal = getLocalStorageItem(constants.prompt_isFold);
    const localTeamId = getLocalStorageItem(constants.prompt_teamId);
    const isTuituiBoard = getLocalStorageItem(constants.prompt_isTuituiBoard);
    const isZhiYuBoard = getLocalStorageItem(constants.prompt_isZhiYuBoard);
    const isYunPanBoard = getLocalStorageItem(constants.prompt_isYunPanBoard);
    const isCompanyBrainBoard = getLocalStorageItem(constants.prompt_isCompanyBrainBoard);
    const isLlmopsBoard = getLocalStorageItem(constants.prompt_isLlmopsBoard);
    const userSrc = getLocalStorageItem(constants.prompt_src);
    // console.log(router.asPath)

    setBackGroundRouterList(systemRole === '1' ? (localStorage.getItem(constants.prompt_src) == 'agent' ? systemSuperManagerRouters : IsNotAgentsystemSuperManagerRouters) : (localStorage.getItem(constants.prompt_src) == 'agent' ? systemManagerRouters : IsNotAgentsystemManagerRouters));
    setIsFold(isFoldLocal === "yes");
    setUserInfo(prevUserInfo => ({ ...prevUserInfo, username: userName || '', }));

    setIsMobile(isMobileDevice());

    if (!isUserLoggedIn(userName) && isNotHomePage(router.asPath) && !isNullOrEmpty(localTeamId)) { // 未登录状态、非首页、有teamId的才会存储当前未登录页面路径
      handleUnloggedInWithTeamId(localTeamId);
    } else if ((!isUserLoggedIn(userName) && isNotHomePage(router.asPath)) || (isChangeLocationSearch())) {
      goToSignalLoginFromBoard();
    }

    if (isTuituiBoardActive(isTuituiBoard)) {
      setIsFromTuituiBoard(true);
      setLocalStorageItem(constants.prompt_isTuituiBoard, "true");
      setLocalStorageItem(constants.prompt_platform, 'tuituiBoard');
    }

    if (isZhiYuBoardActive(isZhiYuBoard)) {
      setIsFromZhiYuBoard(true);
      setLocalStorageItem(constants.prompt_isZhiYuBoard, "true");
      setLocalStorageItem(constants.prompt_platform, 'zhiyuBoard');
    }

    if (isYunPanBoardActive(isYunPanBoard)) {
      setIsFromYunPanBoard(true);
      setLocalStorageItem(constants.prompt_isYunPanBoard, "true");
      setWaibuProjectId(getCurrentUrlParamValue("project_id") || '');       //云盘也需要处理成和llmpos一样的逻辑,用project_id去换teamid
      setLocalStorageItem(constants.prompt_platform, 'yunpanBoard');
      setLocalStorageItem(constants.prompt_yunPanParentOrigin, getCurrentUrlParamValue("parent_origin") || '');
    }

    if (isCompanyBrainBoardActive(isCompanyBrainBoard)) {
      setIsFromCompanyBrainBoard(true);
      setLocalStorageItem(constants.prompt_isCompanyBrainBoard, "true");
      onCompanyBrainValue(true);
      setLocalStorageItem(constants.prompt_platform, 'companyBrain');
    }
    else {
      onCompanyBrainValue(false);
    }

    if (isLlmopsBoardActive(isLlmopsBoard)) {
      setIsFromLlmopsBoard(true);
      setLocalStorageItem(constants.prompt_isLlmopsBoard, "true");
      setWaibuProjectId(getCurrentUrlParamValue("project_id") || '');
      setLocalStorageItem(constants.prompt_platform, 'llmopsBoard');
    }


    // 更新当前登录的平台
    if (isPlatformActive()) {
      let platformLocal = getLocalStorageItem(constants.prompt_platform) || '';
      setPlatformValue(platformLocal as string);
      console.log(getCurrentUrlParamValue("project_id"))
      setWaibuProjectId(getCurrentUrlParamValue("project_id") || '');
    }
  }, []);

  useEffect(() => {
    let noVisibleData: any = [constants.prompt_isZhiKeBoard];
    if (noVisibleData.includes(platformValue)) {
      setIsVisibleLoginOut(false);
    } else {
      setIsVisibleLoginOut(true);
    }
  }, [platformValue]);

  // location search
  const isChangeLocationSearch = () => {
    let searchVal = getLocalStorageItem(constants.prompt_isReloadLoginBySearch) || '';
    // console.log('menu', location.search, searchVal, location.search !== searchVal)
    const isZhiYuBoard = getLocalStorageItem(constants.prompt_isZhiYuBoard);
    return isZhiYuBoardActive(isZhiYuBoard) && searchVal !== location.search && isReloadLoginBySearch;
  }

  // Helper functions
  const getLocalStorageItem = (key: string) => {
    return localStorage.getItem(key);
  }

  const setLocalStorageItem = (key: string, value: string) => {
    localStorage.setItem(key, value);
  }

  const isMobileDevice = () => {
    const userAgent = navigator.userAgent.toLowerCase();
    const isMobileDevice = /mobile|android|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent);
    return isMobileDevice;
  }

  const isUserLoggedIn = (userName: string | null) => {
    return userName && userName.length > 0;
  }

  const isNotHomePage = (path: string) => {
    return path !== '/';
  }

  const handleUnloggedInWithTeamId = (localTeamId: string | null) => {
    let url = new URL(document.location.href);
    let params = new URLSearchParams(url.search);
    let teamId = params.get("teamId");
    if (teamId && teamId == localTeamId) {
      localStorage.setItem(constants.prompt_targetUrl, `${document.location.href}`);
    }
    goToSignalLoginFromBoard();
  }

  const isTuituiBoardActive = (isTuituiBoard: string | null = '') => {
    return window.location.href.indexOf("tuituiBoard") !== -1 || isTuituiBoard === "true";
  }

  // return isZhiYuBoard === "true" || (window.location.href.indexOf("zhiyuBoard") !== -1 && window.location.hostname === "agent.360.cn");
  const isZhiYuBoardActive = (isZhiYuBoard: string | null = '') => {
    return isZhiYuBoard === "true" || window.location.href.indexOf("zhiyuBoard") !== -1;
  }

  const isYunPanBoardActive = (isYunPanBoard: string | null = '') => {
    return isYunPanBoard === "true" || window.location.href.indexOf("yunpanBoard") !== -1;
  }

  const isCompanyBrainBoardActive = (isCompanyBrainBoard: string | null = '') => {
    return isCompanyBrainBoard === "true" || (window.location.href.indexOf("companyBrain") !== -1);
  }

  const isLlmopsBoardActive = (isLlmopsBoard: string | null = '') => {
    return isLlmopsBoard === "true" || (window.location.href.indexOf("llmopsBoard") !== -1);
  }

  const isPlatformActive = () => {
    let urlParse = getCurrentUrlParamParse();
    let sourceValue = urlParse.source || '';
    let platformLocal = getLocalStorageItem(constants.prompt_platform);
    if (!sourceValue && platformLocal) {
      sourceValue = platformLocal;
    }
    if (!sourceValue && platformValue === '') {
      sourceValue = 'agent';
    }
    if (sourceValue && sourceValue !== 'agent' && !platformList.includes(sourceValue)) {
      setPlatformList((prev: any) => [...prev, sourceValue]);
    }
    setLocalStorageItem(constants.prompt_platform, sourceValue);
    // 来源是zhike,进行是否显示智能体对话的标识添加
    if (sourceValue === constants.prompt_isZhiKeBoard) {
      if ((urlParse.agentChatBtn && urlParse.agentChatBtn === 'false')) {
        setLocalStorageItem('agentChatBtn', 'false');
      } else {
        setLocalStorageItem('agentChatBtn', 'true');
      }
    } else {
      setLocalStorageItem('agentChatBtn', 'true');
    }
    return platformList.includes(sourceValue);
  }

  // 来自推推看板、 织语看板、云盘看板 做单点登录
  const goToSignalLoginFromBoard = async () => {
    if (!isTuituiBoardActive() && !isZhiYuBoardActive() && !isYunPanBoardActive() && !isCompanyBrainBoardActive() && !isLlmopsBoardActive() && !isPlatformActive()) {
      return;
    }
    setIsReloadLoginBySearch(false);
    localStorage.setItem(constants.prompt_isReloadLoginBySearch, location.search);
    let platformLocal = getLocalStorageItem(constants.prompt_platform) || '';

    let res = null;
    if (isYunPanBoardActive()) {
      res = await goToLoginWithYunPanToken();
    } else if (isCompanyBrainBoardActive()) {
      res = await goToLoginWithCompanyBrainToken();
    } else if (isTuituiBoardActive() || isZhiYuBoardActive()) {
      res = await goToLoginWithClientUserInfo(isTuituiBoardActive() ? 1 : 2);
    } else {
      let source_type = '';
      if (platformLocal === constants.prompt_isQpaasBoard) {
        source_type = 'qpaas';
      }
      else if (isLlmopsBoardActive()) {
        source_type = 'llmopsBoard';
      } else if (platformLocal && platformLocal !== 'agent') {
        source_type = platformLocal;
      }
      console.log('source_type', source_type);
      source_type && (res = await goClientUserInfoCommon(source_type));
    }
    if (res && res.token) {
      localStorage.setItem(constants.prompt_authorization, "Bearer " + res.token);
      await getUserInfo();
      await getProjectList(() => {
        // console.log('getProjectList=========>', teamId, waibuProjectId)
        if (isCompanyBrainBoardActive()) {
          try {
            const microApp: any = parent.window.__MicroAPP__ || null;
            const props = microApp.basic.getCurrentAppProps(window);
            if (props.customerReload) {
              microApp && microApp.bus.$emit({
                appName: props.name,
                eventName: props.keys.reloadApp
              });
            } else {
              microApp && microApp.basic.reloadApp(window, {
                url: location.href
              });
            }
          } catch (error) {
            console.log(error);
          }
        } else {
          // console.log('getProjectList=========>refresh', teamId, waibuProjectId, router.asPath)
          // document.location.href = router.asPath;
        }
      });
    } else {
      console.log('waibuPlatform login fail')
      localStorage.setItem(constants.prompt_isTuituiBoard, '');
      localStorage.setItem(constants.prompt_isZhiYuBoard, '');
      localStorage.setItem(constants.prompt_isYunPanBoard, '');
      localStorage.setItem(constants.prompt_isCompanyBrainBoard, '');
      localStorage.setItem(constants.prompt_userName, '');
      localStorage.setItem(constants.prompt_authorization, '');
      localStorage.setItem(constants.prompt_teamId, '');
      localStorage.setItem(constants.prompt_targetUrl, '');
      localStorage.setItem(constants.prompt_isReloadLoginBySearch, '');
      localStorage.setItem(constants.prompt_isQpaasBoard, '')
      localStorage.setItem(constants.prompt_isLlmopsBoard, '')
      localStorage.setItem(constants.prompt_src, '')
      setIsFromTuituiBoard(false);
      setIsFromZhiYuBoard(false);
      setIsFromYunPanBoard(false);
      setIsFromCompanyBrainBoard(false);
      onCompanyBrainValue(false)
      setIsFromLlmopsBoard(false);
      setPlatformValue('');
      localStorage.setItem(constants.prompt_platform, '')
      Router.replace('/');
    }
  }

  // 是否是外部平台的判断
  const isOtherPlatform = () => {
    if (isFromTuituiBoard || isFromZhiYuBoard || isFromYunPanBoard || isFromCompanyBrainBoard || isFromLlmopsBoard || isPlatformActive()) {
      return true;
    }
    return false;
  }

  const jumpUrl = (url: string) => {
    router.push(url, undefined, { shallow: true });
  };

  // 替换路由中的teamId，并重新跳转
  const changeTeamIdAndJumpUrl = (teamId: string) => {
    // const reg = /teamId=(\d*|undefined)/; 
    if (router.asPath.indexOf('teamId') === -1) {
      router.push(router.asPath + (document.location.href.indexOf('?') === -1 ? `?teamId=${teamId}` : `&teamId=${teamId}`));
    } else {
      const path = router.asPath.replace(/teamId=undefined/, `teamId=${teamId}`);
      router.push(path);
    }
  }

  useEffect(() => {
    if (localStorage.getItem(constants.isZhiNao) === "yes") {
      setIsZhiNao(true);
    }

    if (typeof window !== "undefined") {
      const hostname = window.location.hostname;
      setIsOuterNet(waiWangHosts.includes(hostname));
    }
    if (localStorage.getItem(constants.prompt_userName)) { // 已经登录的逻辑
      getUserInfo();
      getProjectList();
    }
  }, [teamId]);

  useEffect(() => {
    if (localStorage.getItem(constants.prompt_userName) &&  !localStorage.getItem(constants.prompt_project_list)) { // 已经登录的逻辑
      getProjectList();
    }
  }, []);

  useEffect(() => {
    if (!teamId && waibuProjectId && localStorage.getItem(constants.prompt_userName)) {
      getProjectList();
    }
  }, [waibuProjectId]);

  useEffect(() => {
    if (querySrc === "zhinao") {
      localStorage.setItem(constants.isZhiNao, "yes");
      setIsZhiNao(true);
    }
  }, [querySrc]);

  /** 内外网登录用户获取用户信息 */
  const getUserInfo = async (): Promise<void> => {
    // console.log('Menu.ts----------------------getUserInfo');
    try {
      const res = await reqUserInfo({});
      // console.log('getUserInfo=======>', res);
      if (isNullOrEmpty(res)) {
        return Promise.reject('User info is empty');
      }
      localStorage.setItem(constants.prompt_userId, res.user_id);
      localStorage.setItem(constants.prompt_userName, res.username);
      localStorage.setItem(constants.prompt_src, res.src);
      localStorage.setItem(constants.prompt_enable_canvas, res.enable_canvas);
      const resUserName = res.username;
      const resSrc = res.src;
      // console.log('resSrc', resSrc);
      setUserInfo({
        ...userInfo,
        user_id: res.user_id,
        username: resUserName || localStorage.getItem(constants.prompt_userName),
        email: res.email,
        phone_number: res.phone_number,
        system_role: res.system_role,
        src: resSrc || localStorage.getItem(constants.prompt_src),
      });
      let localSystemRole = localStorage.getItem(constants.prompt_systemRole);
      let system_role = String(res.system_role);

      // 触发自定义事件通知其他组件
      const event = new CustomEvent(constants.prompt_systemRoleUpdated, {
        detail: { systemRole: res.system_role }
      });
      (typeof window !== 'undefined') && window.dispatchEvent(event);

      if (localSystemRole !== system_role) {
        localStorage.setItem(constants.prompt_systemRole, res.system_role);
        if (res.system_role == 0 || res.system_role == 2) {
          setBackGroundRouterList(localStorage.getItem(constants.prompt_src) == 'agent' ? systemManagerRouters : IsNotAgentsystemManagerRouters);
        } else {
          setBackGroundRouterList(localStorage.getItem(constants.prompt_src) == 'agent' ? systemSuperManagerRouters : IsNotAgentsystemSuperManagerRouters);
        }
      }
      return Promise.resolve();
    } catch (error) {
      console.error('Error in getUserInfo:', error);
      return Promise.reject(error);
    }
  };

  const getProjectList = async (callBack: Callback = () => { }): Promise<void> => {
    try {
      if (localStorage.getItem(constants.prompt_project_list)) { // 走缓存逻辑, 解决闪动问题
        const parsedLocalProjectList = JSON.parse(localStorage.getItem(constants.prompt_project_list) as string);
        setItemIdAndProject(parsedLocalProjectList);
      }
      const res = await reqProjectSelectList({})
      // console.log('🚀 getProjectList=======>', res);
      if (res.context.code !== 0) {
        message.error(res.context.message);
        return;
      }
      if (!localStorage.getItem(constants.prompt_project_list)) {
        localStorage.setItem(constants.prompt_project_list, JSON.stringify(res.data));// 默认是个人项目
        setItemIdAndProject(res.data);
      } else {
        const parsedLocalProjectList = JSON.parse(localStorage.getItem(constants.prompt_project_list) as string);
        // 判断缓存中的项目列表和接口返回的项目列表内容是否一致，不一致则更新缓存
        if (!R.equals(parsedLocalProjectList, res.data)) {
          localStorage.setItem(constants.prompt_project_list, JSON.stringify(res.data));// 默认是个人项目
          setItemIdAndProject(res.data);
        }
      }
      callBack && callBack();
      return Promise.resolve();
    } catch (error) {
      goToUnLoginPage();
      localStorage.setItem(constants.prompt_isFirstLogin, 'false');
      return Promise.reject(error);
    }
  };
  // 判断当前id是否在项目列表中
  const isHasTeam = (list: any, id: number) => {
    return list.some((item: any) => item.id === id && (!waibuProjectId || (waibuProjectId && item.project_id && item.project_id === waibuProjectId)));
  };
  const getProjectIdToTeamId = (list: any) => {
    return list.find((item: any) => waibuProjectId && item.project_id && item.project_id === waibuProjectId);
  };
  const setItemIdAndProject = (list: any[]) => {
    const addProject = {
      id: -1,
      name: "新增项目",
      team_type: -1,
      role: 0
    }
    // 知客平台，隐藏新增项目
    let platformValueLocal = localStorage.getItem(constants.prompt_platform) || '';
    const resList = [constants.prompt_isZhiKeBoard].includes(platformValueLocal) ? [...list] : [...list, addProject];
    if (isNullOrEmpty(teamId) || isNaN(Number(teamId))) {
      let localTeamId = localStorage.getItem(constants.prompt_teamId)
      console.log(resList, teamId, isHasTeam(resList, Number(localTeamId)), waibuProjectId);
      if (isNullOrEmpty(localTeamId) || (!isNullOrEmpty(localTeamId) && !isHasTeam(resList, Number(localTeamId)))) {
        let defaultTeamId = resList[0].id;
        if (waibuProjectId) {
          let TeamIdObj = getProjectIdToTeamId(resList);
          TeamIdObj && TeamIdObj.id && (defaultTeamId = TeamIdObj.id);
        }
        localStorage.setItem(constants.prompt_teamId, defaultTeamId);// 默认是个人项目
        localTeamId = defaultTeamId;
        changeTeamIdAndJumpUrl(localTeamId as string)
      } else {
        changeTeamIdAndJumpUrl(localTeamId as string)
      }
    } else {
      localStorage.setItem(constants.prompt_teamId, teamId as string);// 默认是个人项目
    }
    setProjectList(
      resList.map((item: any) => {
        return {
          label: (
            <div key={item.id}>
              {
                item.id !== -1 && <img src={item.team_type === 4 ? selfProject.src : teamProject.src} width={24} className={menuStyles.teamImg} />
              }
              <span className={menuStyles.teamName}>{item.name}</span>
            </div>
          ),
          value: item.id,
          ...item
        };
      })
    );
    const project = resList.find((item: any) => item.id == teamId)
    setCurrentProject({ ...project, value: project?.id });
    updateRole(project?.role || 2);
  }

  const logout = async () => {
    const res = await reqLogout({});
    setTimeout(() => {
      clearUserInfoAndStatus();
      Router.push("/");
    }, 10);
  };

  const boardLogout = () => {
    localStorage.setItem(constants.prompt_userName, '');
    localStorage.setItem(constants.prompt_authorization, '');
    localStorage.setItem(constants.prompt_userId, "");
    localStorage.setItem(constants.prompt_targetUrl, '');
    localStorage.setItem(constants.prompt_isFirstLogin, 'false');
    localStorage.setItem(constants.prompt_platform, '');
    sessionStorage.setItem('sourcePlatform', '');
    localStorage.removeItem(constants.prompt_enable_canvas)
    document.location.href = '/';
  }

  const logoutWaiWang = async () => {
    const res = await reqLogoutWaiWang({});

    if (typeof window !== "undefined") {
      const jquery: any = window.$;
      jquery.getScript("//s.ssl.qhimg.com/quc/quc7.js").then(function () {
        const q: any = window.QHPass;
        q.signOut();
      });
    }
    // location.reload()
    setTimeout(() => {
      clearUserInfoAndStatus();
      Router.push("/");
    }, 10);
  };

  const clearUserInfoAndStatus = () => {
    setUserInfo({
      user_id: 0,
      username: "",
      email: "",
      phone_number: "",
      // 系统角色，0：无系统权限，1:系统超级管理员 2:系统管理员
      system_role: 0,
      src: "",
    });
    localStorage.clear();
  };
  useEffect(() => {
    const pathname = router.pathname;
    if (!isBackground) {
      // 处理首页路由
      if (pathname === '/homePage') {
        setSelectName('home');
        return;
      }

      // 根据路由判断选中的菜单，路由有可能是子路由，此时要找到name为父节点name值
      let name = routerList.find((item) => {
        if (item.children && item.children?.length > 0) {
          if (item.children.find((childItem) => childItem.path === pathname)) {
            return true
          } else {
            return item.path === pathname
          }
        } else {
          return item.path === pathname
        }
      })?.name || "template";
      if (pathname.includes('actionList')) {
        name = 'tool'
      }
      setSelectName(router.query.templateTab ? name + '-' + router.query.templateTab : name);

    } else {

      let list = localStorage.getItem(constants.prompt_systemRole) === '1' ? (localStorage.getItem(constants.prompt_src) == 'agent' ? systemSuperManagerRouters : IsNotAgentsystemSuperManagerRouters) : (localStorage.getItem(constants.prompt_src) == 'agent' ? systemManagerRouters : IsNotAgentsystemManagerRouters)
      // let list = localStorage.getItem(constants.prompt_systemRole) === '1' ? systemSuperManagerRouters : systemManagerRouters
      // const name =
      //   list.find((item) => item.path === pathname)?.name || "backgroundProject";
      // const name = list.find((item) => {
      //   if (item.children && item.children?.length > 0) {
      //     if (item.children.find((childItem) => childItem.path === pathname)) {
      //       return true
      //     } else {
      //       return item.path === pathname
      //     }
      //   } else {
      //     return item.path === pathname
      //   }
      // })?.name || "backgroundProject";
      const name = list.find((item) => {
        // 使用类型来判断是否有 children 字段
        if ((item as any).children && (item as any).children.length > 0) {
          return (item as any).children.find((childItem: any) => childItem.path === pathname) || item.path === pathname;
        }
        return item.path === pathname; // 对于不含 children 的项
      })?.name || "backgroundProject";
      setSelectName(name);

    }

  }, [router.pathname, router.query.templateTab]);

  useEffect(() => {
    const pathname = router.pathname;
    setIsSelectAgentHelper(pathname.includes('agentCreate'))
  }, [router.pathname])

  useEffect(() => {
    const pathname = router.pathname;
    setIsSelectAgentHelper(pathname.includes('agentCreate'))
  }, [router.pathname])


  const clickDocHandle = () => {
    // 跳转到 http://doc.agent.qihoo.net/intro.html
    // const hostname = window.location.hostname;
    // if (hostname === "agent.360.cn" || hostname === "agent.360.com") {
    //   window.open("http://doc.agent.360.cn/intro.html");
    // } else {
    //   window.open("http://doc.agent.qihoo.net/intro.html");
    // }
    window.open("https://easydoc.soft.360.cn/doc?project=e0dafabd6582cf6faa2290b953f105e0&doc=c90cb1593ea4a909dbb18f7fdd618a3d&config=title_menu_toc");
  }

  const feedbackHandel = () => {
    setOpenFeedbackModal(true)
  }
  //这个方法可以只做跳转
  const changeProjectHandle = (key: number) => {
    if (key === -1) {
      setShowCreateProjectModal(true);
    } else {
      const pathname = router.pathname;
      let path = '';
      if (!isBackground) {
        // 根据路由判断选中的菜单，路由有可能是子路由，此时要找到name为父节点name值
        path = routerList.find((item) => {
          if (item.children && item.children?.length > 0) {
            if (item.children.find((childItem) => childItem.path === pathname)) {
              return true
            } else {
              return item.path === pathname
            }
          } else {
            return item.path === pathname
          }
        })?.path || "/flowList";
      } else {
        // path =
        // backGroundRouterList.find((item) => item.path === pathname)?.name || "backgroundProject";
        path = backGroundRouterList.find((item) => {
          if (item.children && item.children?.length > 0) {
            if (item.children.find((childItem: any) => childItem.path === pathname)) {
              return true
            } else {
              return item.path === pathname
            }
          } else {
            return item.path === pathname
          }
        })?.path || "backgroundProject";
      }
      const currentProject = projectList.find((item) => item.id === key);
      const tabUrl = router.query.templateTab ? '&templateTab=' + router.query.templateTab : ''
      jumpUrl(`${path}?teamId=` + currentProject.id + tabUrl);
      localStorage.setItem(constants.prompt_teamId, currentProject.id);
      currentProject && setCurrentProject(currentProject)
      updateRole(currentProject?.role || 2);
  }
  }

  const items: MenuProps['items'] = projectList.map((item, index) => {
    return {
      key: item.id,
      label: (
        <div className={menuStyles.selectProItem} onClick={() => { changeProjectHandle(item.id) }}>
          <div className={menuStyles.selectProItemContent}>
            <img src={item.team_type === 4 ? selfProject.src : (item.team_type === -1 ? menuAddIcon.src : teamProject.src)} width={24} className={menuStyles.selectProItemImg} />
            <span className={menuStyles.selectProItemTitle} style={{ color: item.id === -1 ? '#1D7CFF' : '#1B2532' }}>{item.name}</span>
          </div>
          {(currentProject.id === item.id) ? <img className={menuStyles.selectProItemTrue} src={commonSelectIcon.src} alt="" /> : null}
        </div>
      ),
    }
  })

  const goToUnLoginPage = () => {
    // 回到首页
    document.location.href = '/';
    localStorage.setItem(constants.prompt_isFirstLogin, 'true');// 点击标签后不自动登录
  }

  //选中agent助手并跳转到相应的页面
  const jumpAgentHelper = () => {
    setIsSelectAgentHelper(true)
    // console.log('isSelectAgentHelper', isSelectAgentHelper)
    jumpUrl('/agentCreate?' + "teamId=" + teamId)
  }

  return (
    <>
      {!isFullScreen &&
        <>
          <div
            className={menuStyles.menuBg + " " + (isFold ? menuStyles.isFlod : "") + " " + (isFromCompanyBrainBoard ? menuStyles.menuBgZhiNao : "")}
            onMouseEnter={() => {
              setShowFold(true);
            }}
            onMouseLeave={() => {
              setShowFold(false);
            }}
          >
            <div className={menuStyles.logoWrapper}>
              <a onClick={goToUnLoginPage} style={{ cursor: 'pointer' }}>
                {isFold && (
                  <img src={logo.src} width={20} className={menuStyles.logo} />
                )}
                {!isFold && (
                  <img
                    src={agentLogo.src}
                    className={menuStyles.agent}
                  />
                )}
              </a>
            </div>

            <div style={{ width: '100%', height: `calc(100vh - ${isFromCompanyBrainBoard ? '93' : '84'}px)`, overflowY: 'auto', display: 'flex', flexDirection: 'column', alignItems: 'flex-start', justifyContent: 'space-between' }}>

              <div className={menuStyles.menuList + " normalFont"}>
              <div>
                {/* 主页 */}
                {!isBackground && (
                  <div
                    className={
                      menuStyles.menuItem +
                      " " +
                      (mouserEnterName === "home" || selectName === "home" ? menuStyles.menuActiveItem : "")
                    }
                    onClick={() => jumpUrl("/homePage?teamId=" + teamId)}
                    onMouseEnter={() => {
                      setMouserEnterName("home");
                    }}
                    onMouseLeave={() => {
                      setMouserEnterName("");
                    }}
                  >
                    {isFold ? (
                      <Tooltip placement="leftTop" title="主页">
                        {mouserEnterName === "home" ||
                          selectName === "home" ? (
                          <img src={homeActivate.src} width={22} />
                        ) : (
                          <img src={homeDefault.src} width={22} />
                        )}
                      </Tooltip>
                    ) : (
                      <>
                        {mouserEnterName === "home" ||
                          selectName === "home" ? (
                          <img src={homeActivate.src} width={22} />
                        ) : (
                          <img src={homeDefault.src} width={22} />
                        )}
                        {!isFold && <span>主页</span>}
                      </>
                    )}
                  </div>
                )}

                  {/* {!isFold && !isBackground && <div className={menuStyles.templateLabel}>空间</div>} */}
                  <div className={menuStyles.projectWrapper}>
                    {!isBackground &&
                      (!isFold ? (
                        <Dropdown overlayClassName={menuStyles.dropdownWrapper + ' menu_dropdown'} trigger={['click']} menu={{ items }} overlayStyle={{ boxSizing: 'border-box', boxShadow: '0px 4px 10px 5px rgba(0, 0, 0, 0.10)', borderRadius: '4px', maxHeight: '400px', overflowY: 'auto' }}>
                          <Space>
                            <div className={menuStyles.dropdownContent}>
                              <div className={menuStyles.dropdownContentLeft}>
                                <img src={currentProject.team_type == 4 ? selfProject.src : teamProject.src} width={24} />
                                <div className={menuStyles.dropdownContentLeftTitle}>{currentProject.name}</div>
                              </div>

                              <img src={dropdownIcon.src} width={16} className={menuStyles.dropDownIcon} />
                            </div>
                          </Space>
                        </Dropdown>
                      ) : (
                        <div className={menuStyles.projectInfo}>
                          <img
                            src={currentProject.team_type == 4 ? selfProject.src : teamProject.src}
                            width={20}
                          />
                        </div>
                      ))}
                  </div>
                </div>

                {!isBackground &&
                  routerList.map((item, index) => {
                    return (
                      <div
                        key={item.name}
                        className={
                          menuStyles.menuItem +
                          " " +
                          (mouserEnterName === item.name || selectName === item.name ? menuStyles.menuActiveItem : "")
                        }
                        onClick={() => jumpUrl(item.path + "?teamId=" + teamId)}
                        onMouseEnter={() => {
                          setMouserEnterName(item.name);
                        }}
                        onMouseLeave={() => {
                          setMouserEnterName("");
                        }}
                      >
                        {isFold ? (
                          <Tooltip placement="leftTop" title={item.label}>
                            {mouserEnterName === item.name ||
                              selectName === item.name ? (
                              <img src={item.selectIcon.src} width={22} />
                            ) : (
                              <img src={item.icon.src} width={22} />
                            )}
                          </Tooltip>
                        ) : (
                          <>
                            {mouserEnterName === item.name ||
                              selectName === item.name ? (
                              <img src={item.selectIcon.src} width={22} />
                            ) : (
                              <img src={item.icon.src} width={22} />
                            )}
                            {!isFold && <span>{item.label}</span>}
                          </>
                        )}
                      </div>
                    );
                  })}

                {isBackground && (
                  backGroundRouterList.map((item, index) => {
                    return (
                      <div
                        key={item.name}
                        className={
                          menuStyles.menuItem +
                          " " +
                          (mouserEnterName === item.name || selectName === item.name ? menuStyles.menuActiveItem : "")
                        }
                        onClick={() => jumpUrl(item.path)}
                        onMouseEnter={() => {
                          setMouserEnterName(item.name);
                        }}
                        onMouseLeave={() => {
                          setMouserEnterName("");
                        }}
                      >
                        {isFold ? (
                          <Tooltip placement="leftTop" title={item.label}>
                            {mouserEnterName === item.name ||
                              selectName === item.name ? (
                              <img src={item.selectIcon.src} width={22} />
                            ) : (
                              <img src={item.icon.src} width={22} />
                            )}
                          </Tooltip>
                        ) : (
                          <>
                            {mouserEnterName === item.name ||
                              selectName === item.name ? (
                              <img src={item.selectIcon.src} width={22} />
                            ) : (
                              <img src={item.icon.src} width={22} />
                            )}
                            {!isFold && <span>{item.label}</span>}
                          </>
                        )}
                      </div>
                    );
                  })
                )}

              </div>
              <div style={{ width: '100%' }}>
                {
                  !isBackground && (
                    <div className={isFold ? menuStyles.docWrapper1 : menuStyles.docWrapper}>
                      <div className={menuStyles.docWrapperItem + ' ' + (isFold ? menuStyles.docWrapperFoldItem : '')} onClick={clickDocHandle}>
                        {isFold ? (
                          <Tooltip placement="leftTop" title="文档中心">
                            <img src={docIcon.src} alt="" width={16} className={menuStyles.docWrapperItemIcon} />
                          </Tooltip>
                        ) : (
                          <>
                            <img src={docIcon.src} alt="" width={16} className={menuStyles.docWrapperItemIcon} />
                            <div className={menuStyles.docText}>文档中心</div>
                          </>
                        )}
                      </div>
                    </div>
                  )
                }
                {!isFold && !isBackground ? (<div className={menuStyles.menuGapLine}></div>) : null}
                {!isFromCompanyBrainBoard && <div className={menuStyles.userWrapper + " normalFont"}>
                  {userInfo.username && !isBackground ? (
                    <Popover
                      title=""
                      trigger="hover"
                      arrow={false}
                      placement={"topLeft"}
                      content={
                        <div className={menuStyles.userInfoWrapper}>
                          <div className={menuStyles.userNameBg}>
                            <img
                              src={user.src}
                              width={24}
                              className={menuStyles.headSculpture}
                            />
                            <span className={"boldFont"}>{userInfo.username}</span>
                          </div>
                          <div className={menuStyles.userInfoInner}>
                            {
                              userInfo.system_role != 0 ? (
                                <div className={menuStyles.userInfoList1 + " normalFont"}>
                                  <div className={menuStyles.userInfoItem1}>
                                    <img src={backgroundManageIcon.src} width="14" />
                                    <a onClick={() => window.open('/background/projectList', isFromZhiYuBoard ? "_self" : "_blank", console.log('dhskufhjkdshf'))}>
                                      后台管理
                                    </a>
                                  </div>
                                </div>) : null
                            }
                            {isVisibleLoginOut && <div className={menuStyles.userInfoItem1}>
                              <img src={logoutIcon.src} width="14" />
                              <span onClick={isOtherPlatform() ? boardLogout : (isOuterNet ? logoutWaiWang : logout)}>
                                退出登录
                              </span>
                            </div>}
                          </div>
                        </div>
                      }
                    >
                      <div style={{ width: '100%', paddingLeft: !isFold ? '10px' : '0px' }}>
                        <img src={user.src} width={24} height={24} />
                        {!(isFold) && (
                          <span className={menuStyles.userName + " normalFont"}>
                            {userInfo.username}
                          </span>
                        )}
                      </div>
                    </Popover>
                  ) : null}
                </div>}
              </div>
            </div>
            <div
              className={
                menuStyles.flodIcon + " " + ((showFold || isMobile) ? menuStyles.show : "")
              }
              onClick={() => {
                setIsFold(!isFold);
                localStorage.setItem(constants.prompt_isFold, isFold ? "no" : "yes");
              }}
            >
              {isFold ? (
                <img src={doubleArrowRight.src} width="14" />
              ) : (
                <img src={doubleArrowLeft.src} width="14" />
              )}
            </div>
          </div>
          <FeedbackModal openModal={openFeedbackModal} setOpenModal={setOpenFeedbackModal} />
          <CreateNewProjectModal openModal={showCreateProjectModal} setOpenModal={setShowCreateProjectModal} />
        </>
      }
    </>
  );
}
function useParams<T>() {
  throw new Error("Function not implemented.");
}
