import React, { useState, useEffect, useRef } from "react";
import { Tag, Dropdown, Modal } from "antd";
import commonListCardStyle from "@/styles/CommonListCard.module.scss";
import flowDefaultImg from '@/images/commonList/common-flow-default.svg'
import apiDefaultImg from '@/images/commonList/common-api-default.svg'
import knowledgeQaDefaultImg from '@/images/commonList/common-knowledgeQa-default.svg'
import knowledgeQdDefaultImg from '@/images/commonList/common-knowledgeQd-default.svg'
import promptDefaultImg from '@/images/commonList/common-prompt-default.svg'

import user from "@/images/menu/user.png";
import itemMore from '@/images/commonList/common-item-more.svg'
import flowFormInteractive from '@/images/flowInteractive.png';
import type { MenuProps } from 'antd';

import { clone } from "ramda";
import Show from "@/components/commonComponents/show/Show";
import useCurrentProjectRole from "@/hooks/useCurrentProjectRole";


const CommonListCard = (props: any) => {
    const { userRole } = useCurrentProjectRole();
    const { onClickFunc, onCopyFunc, onDeleteFunc, onTopFunc, pageName, item, editNameHandel, onImportFunc, isFromLlmopsBoard } = props
    const [commonItem, setCommonItem] = useState<any>(item);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [reNameValue, setReNameValue] = useState(item?.name || '');
    const [defaultImage, setDefaultImage] = useState(flowDefaultImg);
    const [imageBgColor, setImageBgColor] = useState('linear-gradient(180deg, #90BEFF 0%, #1D7CFF 100%)');
    const inputRef = useRef<any>(null);

    useEffect(() => {
        let commonItem = clone(item);
        if (pageName === 'flow') {
            setDefaultImage(flowDefaultImg);
            setImageBgColor('linear-gradient(180deg, #90BEFF 0%, #1D7CFF 100%)');
            commonItem = {
                id: item.id,
                chain_no: item.chain_no,
                name: item.title,
                description: item.desc,
                status: item.publish_status,
                image: item.images || '',
                userName: item.user_name || '',
                flow_type: item.flow_type,
                is_top: item?.is_top || 2,
                version: item?.version || '',
            }
        } else if (pageName === 'api') {
            setDefaultImage(apiDefaultImg);
            setImageBgColor('linear-gradient(180deg, #9499FF 0%, #7756FC 100%)');
            commonItem = {
                id: item.id,
                name: item.name,
                description: item.detail,
                status: item.publish_status,
                image: item?.images || '',
                userName: item?.user_name || '',
                is_top: item?.is_top || 2,
            }
        } else if (pageName === 'knowledge-chat') {
            setDefaultImage(knowledgeQaDefaultImg);
            setImageBgColor('linear-gradient(180deg, #FFD89C 0%, #F4B14C 100%)');
            commonItem = {
                ...item,
                image: item.imageUrl || '',
            }
        } else if (pageName === 'knowledge-data') {
            setDefaultImage(knowledgeQdDefaultImg);
            setImageBgColor('linear-gradient(180deg, #FFD89C 0%, #F4B14C 100%)');
            commonItem = {
                ...item,
                image: item.imageUrl || '',
            }
        } else if (pageName === 'prompt') {
            setDefaultImage(promptDefaultImg);
            setImageBgColor('linear-gradient(180deg, #AAE4A4 0%, #74BE6C 100%)');
            commonItem = {
                id: item.template_id,
                name: item.name,
                chain_no: item.chain_no,
                description: item.desc,
                status: item.status,
                image: item?.image || '',
                userName: item?.creator || '',
                is_top: item?.is_top || 2,
            }
        }
        setCommonItem(commonItem);
        setReNameValue(commonItem?.name || '')
    }, [pageName, item])

    // flow卡片/提示词卡片-操作项
    const items: MenuProps['items'] = [
        {
            label: '重命名',
            key: '1',
        },
        {
            label: '复制',
            key: '2',
            disabled: item?.version == 5 ? false : true
        },
        // ...(pageName === 'flow' ? [{ label: '导出', key: '3' }] : []),
        {
            label: '删除',
            key: '4',
        },
        // ...(userRole == 1 ? [{ label: commonItem.is_top == 1 ? '取消置顶' : '置顶', key: '5' }] : []),
    ];

    // 插件卡片-操作项
    const pluginItems: MenuProps['items'] = [
        {
            label: '重命名',
            key: '1',
        },
        {
            label: '删除',
            key: '2',
        },
        ...(userRole == 1 ? [{ label: commonItem.is_top == 1 ? '取消置顶' : '置顶', key: '5' }] : []),
    ];

    const knowledgeItems: MenuProps['items'] = [
        {
            label: '重命名',
            key: '1',
        },
        {
            label: '删除',
            key: '3',
        },
    ];


    const handleMenuClick: MenuProps['onClick'] = (e) => {
        const event = e.domEvent;
        event.stopPropagation();
        switch (e.key) {
            case '1':
                editNameHandel && editNameHandel(item);
                break
            case '2':
                onCopyFunc && onCopyFunc(commonItem.id);
                break
            case '4':
                setIsModalOpen(true);
                break
            case '3':
                onImportFunc && onImportFunc(commonItem.id, commonItem.name, commonItem?.version);
                break
            case '5':
                onTopFunc && onTopFunc(item);
                break
        }
    };
    const handlePluginMenuClick: MenuProps['onClick'] = (e) => {
        const event = e.domEvent;
        event.stopPropagation();
        switch (e.key) {
            case '1':
                editNameHandel && editNameHandel(item);
                break
            case '2':
                setIsModalOpen(true);
                break
            case '5':
                onTopFunc && onTopFunc(item);
                break
        }
    };
    // 确定删除
    const handleModalOk = () => {
        // if(pageName === 'flow') {
        //     onDeleteFunc && onDeleteFunc(commonItem.chain_no);
        // }else {
        onDeleteFunc && onDeleteFunc(commonItem.id);
        // }
        setIsModalOpen(false);
    }

    const handleModalCancel = () => {
        setIsModalOpen(false);
    }

    const menuProps = {
        items: pageName === 'knowledge-chat' || pageName === 'knowledge-data' ? knowledgeItems : (pageName === 'api' ? pluginItems : items),
        onClick: pageName === 'api' ? handlePluginMenuClick : handleMenuClick
    };
    const classNames = {
        content: 'knowledge-item-modal-content',
    };

    const getKnowledgeStatus = (status: string) => {
        if (status === 'waiting') {
            return '待训练'
        } else if (status === 'doing') {
            return '训练中'
        } else if (status === 'succeeded') {
            return '训练完成'
        } else if (status === 'failed') {
            return '训练失败'
        }
    }
    const getKnowledgeSplitStatus = (status: number) => {
        if (status === 1) {
            return '待分割'
        } else if (status === 2) {
            return '分割中'
        } else if (status === 3) {
            return '分割成功'
        } else if (status === 4) {
            return '分割失败'
        }
    }

    const nameDisplay = (item: any) => {
        return pageName === 'api' ? item.user_name : item.userName;
    }

    return (
        <>
            <div className={commonListCardStyle.container} onClick={() => { onClickFunc && onClickFunc(commonItem?.id) }}>
                <div className={commonListCardStyle.cardLeft}>
                    <div className={commonListCardStyle.commonHeaderImg} style={{ background: imageBgColor }}>
                        <img src={commonItem.image ? commonItem.image : defaultImage.src} alt="" />
                    </div>
                </div>
                <div className={commonListCardStyle.cardRight}>
                  <div className={commonListCardStyle.commonHeader}>
                    <div className={commonListCardStyle.commonHeaderTop}>
                        <div className={commonListCardStyle.commonHeaderTitle}>
                            <span>{reNameValue}</span>
                        </div>
                        {!isFromLlmopsBoard && (!pageName.startsWith('knowledge')) && (commonItem?.status === 2 ? <Tag bordered={false} color="processing">部署</Tag> : null)}
                        <Dropdown menu={menuProps} overlayClassName={commonListCardStyle.dropMenuBox}>
                            <img src={itemMore.src} alt='' className={commonListCardStyle.itemMoreImg + ' ' + (pageName === 'flow' ? commonListCardStyle.itemMoreImgFlow : '')} onClick={(e) => e.stopPropagation()} />
                        </Dropdown>
                    </div>
                    <div className={commonListCardStyle.commonHeaderDesc}>
                        {commonItem?.description}
                    </div>
                    {
                        pageName === 'flow' && commonItem?.flow_type === 2 && <div style={{
                            position: 'absolute',
                            top: '-2px',
                            right: '-3px'
                        }}>
                            <img src={flowFormInteractive.src} alt="" width='48' height='48' />
                        </div>
                    }
                    <Show
                        when={commonItem?.is_top == 1}
                        children={<div className={commonListCardStyle.topIcon}>置顶</div>}
                    />
                </div>
                <div className={commonListCardStyle.footer}>
                    <div className={commonListCardStyle.footerUser}>
                        <span>创建者: {commonItem?.userName}</span>
                    </div>
                    <div>
                        {(pageName === 'knowledge-data') && (commonItem?.split_status === 3 ? <Tag color="green">分割成功</Tag> : <Tag style={{ color: '#626F84' }}>{getKnowledgeSplitStatus(commonItem?.split_status)}</Tag>)}
                        {(pageName === 'knowledge-chat' || pageName === 'knowledge-data') && (commonItem?.status === 'succeeded' ? <Tag color="green">训练完成</Tag> : <Tag style={{ color: '#626F84' }}>{getKnowledgeStatus(commonItem?.status)}</Tag>)}
                    </div>
                </div>
                </div>
            </div>
            <Modal title="提示" classNames={classNames} centered={true} destroyOnClose={true} open={isModalOpen} onOk={handleModalOk} onCancel={handleModalCancel} okText="确认"
                cancelText="取消">
                <p>确定要删除这条数据吗？</p>
            </Modal>
        </>
    );
};

export default CommonListCard;