import React, { useState } from 'react';
import { Input, Button } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import styles from './index.module.scss';

interface SearchBoxProps {
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  onSearch?: (value: string) => void;
  buttonText?: string;
}

export default function SearchBox({
  placeholder = "请输入关键词搜索",
  value = '',
  onChange,
  onSearch,
  buttonText = "创建多智能体蜂群"
}: SearchBoxProps) {
  const [internalValue, setInternalValue] = useState('');

  // 使用受控模式时使用外部value，否则使用内部state
  const searchValue = onChange ? value : internalValue;
  const setSearchValue = onChange ? onChange : setInternalValue;

  const handleSearch = () => {
    onSearch?.(searchValue);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  return (
    <div className={styles.searchBoxContainer}>
      <div className={styles.searchInput}>
        <Input
          size="middle"
          placeholder={placeholder}
          value={searchValue}
          onChange={(e) => setSearchValue(e.target.value)}
          onKeyDown={handleKeyDown}
          prefix={<SearchOutlined className={styles.searchIcon} />}
        />
      </div>
      <Button
        type="primary"
        size="large"
        className={styles.createButton}
        onClick={() => {/* 处理创建按钮点击 */ }}
      >
        {buttonText}
      </Button>
    </div>
  );
}