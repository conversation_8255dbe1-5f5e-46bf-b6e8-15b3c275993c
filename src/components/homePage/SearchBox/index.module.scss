.searchBoxContainer {
  display: flex;
  align-items: center;
  gap: 16px;
}

.searchInput {
  flex: 1;

  :global(.ant-input-affix-wrapper) {
    background-color: #ffffff;
    border-radius: 8px;
    border: 1px solid #d5d7de;
    padding: 5px 12px;
    display: flex;
    align-items: center;
    gap: 8px;

    &:hover {
      border-color: #1c53e0;
    }

    &:focus-within {
      border-color: #1c53e0;
      box-shadow: 0 0 0 2px rgba(28, 83, 224, 0.1);
    }
  }

  :global(.ant-input) {
    border: none;
    box-shadow: none;
    font-family: 'PingFang SC', sans-serif;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    color: #9ba7ba;
    background: transparent;
    padding: 0;

    &::placeholder {
      color: #9ba7ba;
    }
  }

  :global(.ant-input-prefix) {
    margin-right: 0;
  }
}

.searchIcon {
  color: #9ba7ba;
  font-size: 16px;
}

.createButton {
  border: none;
  border-radius: 8px;
  padding: 0 24px;
  height: 32px;
  padding: 12px 16px;
  color: var(---, #FFF);
  font-feature-settings: 'liga' off, 'clig' off;
  font-family: "PingFang SC";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;

  /* 157.143% */
  &:hover {
    opacity: 0.9;
  }
}