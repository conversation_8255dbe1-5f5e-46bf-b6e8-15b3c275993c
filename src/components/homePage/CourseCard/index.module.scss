.courseCard {
  width: 712px;
  min-height: 219.92190551757812px;
  border-radius: 12px;
  background-repeat: no-repeat;
  background-size: auto 219.92190551757812px;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  }
}

.content {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1;
}

.leftTopIcon {
  width: 40px;
  height: 40px;
  margin-bottom: 16px;

  img,
  svg {
    width: 100%;
    height: auto;
    object-fit: cover;
  }
}

.textContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.bigText {
  font-size: 20px;
  font-weight: 600;
  color: #000;
  margin: 0 0 8px 0;
  line-height: 1.3;
}

.smallText {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.6);
  margin: 0;
  line-height: 1.4;
}