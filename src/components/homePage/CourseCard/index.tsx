import React from "react"
import styles from "./index.module.scss"

interface CourseCardProps {
  leftTopIcon?: React.ReactNode
  bigText?: string
  smallText?: string
  backgroundPic?: string
  backgroundColor?: string
  url?: string
}

export default function CourseCard({
  leftTopIcon,
  bigText,
  smallText,
  backgroundPic,
  backgroundColor = "#f0f0f0",
  url
}: CourseCardProps) {
  const cardStyle = backgroundPic
    ? { backgroundImage: `url(${backgroundPic})` }
    : { backgroundColor }

  const handleUrl = () => {
    if (url) {
      window.open(url, "_blank")
    } else {
      console.error("URL is undefined")
    }
  }

return (
  <div className={styles.courseCard} style={cardStyle} onClick={handleUrl}>
    <div className={styles.content}>
      <div className={styles.leftTopIcon}>{leftTopIcon || null}</div>
      <div className={styles.textContent}>
        <h3 className={styles.bigText}>{bigText || ""}</h3>
        <p className={styles.smallText}>{smallText || ""}</p>
      </div>
    </div>
  </div>
)
}
