import React, { useEffect, useRef, useImperativeHandle, forwardRef, useState } from "react";
import commonListStyle from "@/styles/CommonListOuter.module.scss";
import { Row, Col, message, Spin, Skeleton, Divider } from "antd";
import CommonListCard from "@/components/common/CommonListCard";
import CreateCommonModal from "@/components/common/CreateModal";
import CommonListCardForYunPan from "@/components/common/CommonListCardForYunPan";
import { initBlockList, initInteractionBlockList } from '@/config/flowConfig';

import { useRouter } from "next/router";
import {
  reqAddFlow,
  reqDeleteFlow,
  reqFlowList,
  reqCopyFlow,
  reqImportFlow,
} from "@/service/flow3.0";

import { getCurrentUrlParamValue } from "@/utils/url";
import InfiniteScroll from "react-infinite-scroll-component";
import { isNullOrEmpty } from "@/utils/common";
// 列表页既有v2 也有 v3 
import flowExportJsonFile from "@/utils/FlowConstants/flowExportJsonFile";
import { reqItemTopUp } from "@/service/common";
import { notify } from "@/utils/messageManager";
import ListEmpty from "../commonComponents/listEmpty/ListEmpty";

const FlowListIndex = forwardRef((props: any, ref) => {
  const {moreAgentTeams, setMoreAgentTeams} = props;
  const [dataList, setDataList] = useState<any[]>([]);
  const [createNewModal, setCreateNewModal] = React.useState(false);

  const router = useRouter();
  const teamId = router.query.teamId;
  const [addLoading, setAddLoading] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isNeedRefresh, setIsNeedRefresh] = useState(false);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(28);
  const [hasMore, setHasMore] = useState(false);
  const [total, setTotal] = useState(10);
  const [searchValue, setSearchValue] = useState('');
  const [propelValue, setPropelValue] = useState(0);
  const [statusValue, setStatusValue] = useState(0);
  const [isOperateItem, setIsOperateItem] = useState<any>({});
  const [isEditNameModal, setIsEditNameModal] = useState(false);
  const [isImportSuccess, setIsImportSuccess] = useState(true);
  const [isFromYunPanBoard, setIsFromYunPanBoard] = useState(false);

  const flowCreate = router.query.flowCreate;

  // 创建代码
  const createNewHandel = () => {
    setCreateNewModal(true)
  }
  // 搜索代码
  const searchDoneHandle = (value: string) => {
    setSearchValue(value)
  }
  // 创建人筛选
  const changePropelHandle = (value: number) => {
    setPropelValue(value)
  }
  // 状态筛选
  const changeStatusHandle = (value: number) => {
    setStatusValue(value)
  }

  useEffect(() => {
    const isFromYunPan = getCurrentUrlParamValue('source') === 'yunpanBoard' || localStorage.getItem('prompt_isYunPanBoard') === 'true';
    setIsFromYunPanBoard(isFromYunPan);
  }, [])

  useEffect(() => {
    setPage(1);
    setHasMore(false);
    teamId && getList(1);
    if (flowCreate == '1') {
      createNewHandel()

      const url = new URL(window.location.href);
      const params = new URLSearchParams(url.search);
      params.set('flowCreate', '');
      url.search = params.toString();
      history.replaceState(null, '', url.href);
    }
  }, [searchValue, propelValue, statusValue, isNeedRefresh, teamId])

  const getList = async (pageS: number = page) => {
    try {
      setLoading(true);
      const params = {
        page: pageS,
        page_size: pageSize,
        keyword: searchValue,
        publish_status: statusValue,// 默认为0，全部，1:未发布，2:已发布
        user_id: propelValue,//用户ID
      }
      const res = await reqFlowList(params)
      if (isNullOrEmpty(res)) {
        setHasMore(false);
        pageS === 1 && setDataList([]);
        return;
      }
      if (res.list.length === 0 || res.list.length < pageSize) {
        setHasMore(false);
      } else {
        setHasMore(true);
        setPage(pageS + 1);
      }
      if (pageS === 1) {
        setDataList(res.list);
        return;
      }
      setDataList([...dataList, ...res.list]);
    } catch (e: any) {
      setHasMore(false);
      message.error(e.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDataDetail = (id: string, flow_type = 1, version, title = '') => {
    const teamId = getCurrentUrlParamValue("teamId");
    let url = version == 5 ? '/flowDetailV3' : '/flowDetailV3'
    if (moreAgentTeams || (title && title.startsWith('专家组队-'))) {
      url = '/flowDetailV4';
    }
    let path = url + `?id=${id}&teamId=${teamId}&flow_type=${flow_type}`;
    if (moreAgentTeams) {
      path = path + '&firstInit=true';
    }
    router.push(path);
  };

  // 创建
  const okCreateHandel = (values: any) => {
    handleAdd(values)
  }

  const onDeleteFunc = async (id: string) => {
    try {
      const res = await reqDeleteFlow({ id });
      if (!isNullOrEmpty(res)) {
        message.success("删除成功");
        setIsNeedRefresh(!isNeedRefresh);
      }
    } catch (e: any) {
      message.error(e.message);
    }
  }
  const onCopyFunc = async (id: string) => {
    try {
      const res = await reqCopyFlow({ template_id: id });
      if (res) {
        message.success("复制成功");
        setIsNeedRefresh(!isNeedRefresh);
      }
    } catch (e: any) {
      message.error(e.message);
    }
  }

  const onImportFunc = async (id: string, fileName: string, version = '') => {
    console.log('id', id)
    flowExportJsonFile(id, fileName, '', version)
  }

  const handleAdd = async (values: any) => {
    setAddLoading(true);
    try {
      interface iConfigObj {
        [key: string]: object;
      }
      let flowConfig: iConfigObj = {};
      const blockKeys = new Array();
      (values.flowType == 2 ? initInteractionBlockList : initBlockList).map(b => {
        return b;
      }).forEach(block => {
        flowConfig[block.id] = block;
        blockKeys.push(block.id);
      })

      console.log('----------------------------------', moreAgentTeams)

      if (moreAgentTeams) {
        values.name = '专家组队-' + values.name;
        flowConfig = {};
      }

      const data = {
        title: values.name,
        desc: values.description,
        images: values.imageUrl,
        flow_type: values.flowType, // 1 任务型 2 交互型
        flow_config: {// flow默认配置项目
          blocks: flowConfig,
          // block_keys: blockKeys
        },
      };
      
      const formData: any = new FormData();
      formData.append('file', values.uploadedFile);
      formData.append('title', values.name);
      formData.append('desc', values.description);
      formData.append('images', values.imageUrl);
      formData.append('flow_type', values.flowType);

      const res = values.importMode == 1 ? await reqAddFlow(data) : await reqImportFlow(formData)
      if (values.importMode == 2 && isNullOrEmpty(res)) {
        setIsImportSuccess(false)
      }
      if (isNullOrEmpty(res)) {
        return;
      }
      console.log('isImportSuccess', isImportSuccess)
      message.success("新增成功");
      handleDataDetail(res.template_id, values.flowType, 4)
      setCreateNewModal(false)
    } catch (e: any) {
      message.error(e.message);
    } finally {
      setAddLoading(false);
    }
  };


  useImperativeHandle(ref, () => ({
    createNewHandel: () => {
      createNewHandel()
    },
    searchDoneHandle: (value: string) => {
      searchDoneHandle(value)
    },
    changePropelHandle: (value: number) => {
      changePropelHandle(value)
    },
    changeStatusHandle: (value: number) => {
      changeStatusHandle(value)
    },
  }));

  const editCompleteHandel = () => {
    setIsNeedRefresh(!isNeedRefresh);
    setIsEditNameModal(false);
  }
  // 点击编辑调用
  const editNameHandel = (item: any) => {
    console.log("item", item)
    let params = {...item};
    if (params.title && params.title.startsWith("专家组队-")) {
      setMoreAgentTeams(true);
      params.title = params.title.replace("专家组队-", "")
    }
    setIsEditNameModal(true);
    setIsOperateItem(params);
  }

  const onTopFunc = async (item: any) => {
    try {
      await reqItemTopUp({ template_id: item.id, template_type: 3, update_type: item.is_top == 1 ? 2 : 1 });
      getList(1);
    } catch (e: any) {
      notify.error(e.message);
    }
  }

  return (
    <>
      <Spin spinning={addLoading || loading} tip="">
        {dataList.length === 0 ? (
          <div style={{
            height: 600
          }}><ListEmpty createTitle="创建技能" createNewHandel={createNewHandel} /></div>
        ) : (
          <div className={commonListStyle.listContainer} id="flowScrollableDiv">
            <InfiniteScroll
              dataLength={dataList.length}
              next={getList}
              hasMore={hasMore}
              style={{ overflowX: 'hidden' }}
              loader={<></>}
              // loader={<Skeleton avatar paragraph={{ rows: 1 }} active />}
              // endMessage={<Divider plain>没有更多数据了</Divider>}
              scrollableTarget={'flowScrollableDiv'}
              scrollThreshold={0.1}
            >
              <div>
                <Row gutter={[12, 12]}>
                  {
                    dataList.map((item, index) => (
                      isFromYunPanBoard ? (
                        // @ts-ignore
                        <Col
                          xs={8} sm={8} md={8} lg={6} xl={6} xxl={4}
                          key={item.name + item.id + item.description}>
                          <CommonListCardForYunPan pageName="flow" editNameHandel={editNameHandel} item={item} onClickFunc={() => { handleDataDetail(item.id, item.flow_type, item.version) }} onDeleteFunc={onDeleteFunc} onCopyFunc={onCopyFunc} onImportFunc={onImportFunc} onTopFunc={onTopFunc} />
                        </Col>
                      ) : (
                        // @ts-ignore
                        <Col
                          span={6}
                          key={item.id}>
                          <CommonListCard pageName="flow" editNameHandel={editNameHandel} item={item} onClickFunc={() => { handleDataDetail(item.id, item.flow_type, item.version, item.title) }} onDeleteFunc={onDeleteFunc} onCopyFunc={onCopyFunc} onImportFunc={onImportFunc} onTopFunc={onTopFunc} />
                        </Col>
                      )
                    ))
                  }
                </Row>
              </div>
            </InfiniteScroll>
          </div >
        )}
        <CreateCommonModal moreAgentTeams={moreAgentTeams} pageName="flow" openModal={createNewModal} setOpenModal={(open: boolean) => {
          setCreateNewModal(open);
          setMoreAgentTeams(open);
        }} okHandel={okCreateHandel} isImportSuccess={isImportSuccess} />
        <CreateCommonModal
          moreAgentTeams={moreAgentTeams}
          pageName="flow"
          openModal={isEditNameModal}
          setOpenModal={(open: boolean) => {
            console.log('----edit', open)
            setIsEditNameModal(open);
            setMoreAgentTeams(open);
          }}
          okHandel={editCompleteHandel}
          baseInfo={isOperateItem}
        />
      </Spin >
    </>
  );
});

export default FlowListIndex;
