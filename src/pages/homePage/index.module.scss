.homePageContainer {
  width: 100%;
  min-height: 100vh;
  min-width: 64.25rem;
  padding: 3.75rem 5rem;
  background:
    linear-gradient(180deg, rgba(247, 249, 252, 0.00) 0%, #F7F9FC 100vh),
    linear-gradient(90deg, #EFF5FF 0%, #E7F0FF 100vh);
  background-color: #E7F0FF;
}

.tutorialSection {
  margin-bottom: 2rem;
  min-width: 87.5rem;
  .courseSection {
    // margin-top: 2rem;
    display: flex;
    justify-content: space-between;
    
  }
}

.agentSection {
  min-width: 1228px;
  .navSection {
    display: flex;
    justify-content: space-between;
    align-items: center;
    // margin: 2rem 0;
    gap: 2.5rem;
  }

  .agentList {
    margin-top: 1.5rem;
  }
}