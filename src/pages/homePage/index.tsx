import { RecoilRoot } from 'recoil'
import React, { useState, useEffect, useMemo } from 'react'
import { Row, Col } from 'antd'
import TopText from '@/components/homePage/TopText'
import TitleText from '@/components/homePage/TitleText'
import CourseCard from '@/components/homePage/CourseCard'
import NavTabs from '@/components/homePage/NavTabs'
import SearchBox from '@/components/homePage/SearchBox'
import AgentCard from '@/components/homePage/AgentCard'
import BackgroundPic from '@/components/homePage/BackgroundPic'
import { getNamiAgentList, AgentItem, AgentCategory } from '@/service/homePage'
import styles from './index.module.scss'

const courseCards = [
  {
    id: 1,
    // leftTopIcon: <div style={{ width: 40, height: 40, background: '#1c53e0', borderRadius: 8 }} />,
    // bigText: '产品功能速览',
    // smallText: '快速了解产品核心功能',
    backgroundPic: 'https://p2.ssl.qhimg.com/t110b9a93017c770b4409ee86c9.png',
    url: 'https://easydoc.soft.360.cn/doc?project=e0dafabd6582cf6faa2290b953f105e0&doc=c90cb1593ea4a909dbb18f7fdd618a3d&config=title_menu_toc'
  },
  {
    id: 2,
    // leftTopIcon: <div style={{ width: 40, height: 40, background: '#c258f7', borderRadius: 8 }} />,
    // bigText: '多智能体蜂群能力精讲',
    // smallText: '深入理解多智能体协作机制',
    backgroundPic: 'https://p5.ssl.qhimg.com/t110b9a930172f3ae3f553a71c4.png',
    url: ''
  }
];

function HomePageContent() {
  const [activeTab, setActiveTab] = useState('all');
  const [allAgentData, setAllAgentData] = useState<AgentCategory[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchValue, setSearchValue] = useState('');

  const handleToChat = (id) => {
    if (id) {
      window.open(`https://www.n.cn/tools/aiagent/chat/${id}`, '_blank')
    }
  }

  // 根据选中的tab和搜索关键词过滤智能体数据
  const filteredAgentCards = useMemo(() => {
    if (!allAgentData.length) return [];

    let agents: AgentItem[] = [];

    if (activeTab === 'all') {
      // 返回所有智能体
      agents = allAgentData.reduce((acc, category) => {
        return acc.concat(category.list);
      }, [] as AgentItem[]);
    } else {
      // 返回指定分类的智能体
      const targetCategory = allAgentData.find(cat => cat.category === activeTab);
      agents = targetCategory ? targetCategory.list : [];
    }

    // 如果有搜索关键词，进行标题过滤
    if (searchValue.trim()) {
      agents = agents.filter(agent =>
        agent.title.toLowerCase().includes(searchValue.toLowerCase())
      );
    }

    return agents;
  }, [allAgentData, activeTab, searchValue]);

  // 获取智能体数据（只请求一次）
  const fetchAllAgentData = async () => {
    setLoading(true);
    try {
      const response = await getNamiAgentList();
      console.log('获取到的完整数据:', response);

      if (response && response.list) {
        setAllAgentData(response.list);
      }
    } catch (error) {
      console.error('获取智能体数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 初始化时获取全部数据
  useEffect(() => {
    fetchAllAgentData();
  }, []);

  return (
    <div className={styles.homePageContainer}>
      {/* 背景图片 */}
      <BackgroundPic />

      {/* 顶部文字 */}
      <TopText
        title="智筑未来 一键搭建超级智能体"
        subtitle="模块化零代码搭建，让智能能力触手可及"
      />

      {/* 新手教程部分 */}
      <div className={styles.tutorialSection}>
        <TitleText
          tabs={['新手教程']}
          activeTab="新手教程"
        />
        <div className={styles.courseSection}>
          {courseCards.map(card => (
            <div>
              <CourseCard
                // leftTopIcon={card?.leftTopIcon}
                // bigText={card?.bigText}
                // smallText={card?.smallText}
                backgroundPic={card?.backgroundPic}
                url={card?.url}
              />
            </div>
          ))}
        </div>
      </div>

      {/* 智能体广场部分 */}
      <div className={styles.agentSection}>
        <TitleText
          tabs={['智能体广场']}
          activeTab="智能体广场"
        />

        {/* 导航和搜索 */}
        <div className={styles.navSection}>
          <NavTabs
            activeTab={activeTab}
            updateActiveTab={setActiveTab}
          />
          <SearchBox
            placeholder="请输入关键词搜索"
            value={searchValue}
            onChange={setSearchValue}
            onSearch={(value) => setSearchValue(value)}
          />
        </div>

        {/* 智能体卡片列表 */}
        <Row gutter={[16, 16]} className={styles.agentList}>
          {loading ? (
            <div style={{ width: '100%', textAlign: 'center', padding: '40px' }}>
              加载中...
            </div>
          ) : (
            filteredAgentCards.map(card => (
              <Col span={6} key={card.id}>
                <AgentCard
                  icon={card.icon}
                  title={card.title}
                  desc={card.greeting || card.intro}
                  tag={card.limited_free ? '限免' : undefined}
                  creator={card.creator}
                  onClick={() => handleToChat(card?.id)}
                />
              </Col>
            ))
          )}
        </Row>
      </div>
    </div>
  )
}

export default function Root() {
  return (
    <RecoilRoot>
      <HomePageContent />
    </RecoilRoot>
  )
}
