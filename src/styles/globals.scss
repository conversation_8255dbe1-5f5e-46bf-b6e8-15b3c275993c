* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

iframe {
  border: 0;
}

html,
body {
  max-width: 100vw;
  overflow: auto;
}

img {
  display: inline-block !important;
}

body {
  margin: 0px auto;
}

ul,
li {
  list-style: none;
  transition: none !important;
  animation: none !important;
}

a {
  color: inherit;
  text-decoration: none;
}

textarea {
  resize: none !important;
}

input::placeholder {
  color: #9BA7BA !important;
}

textarea::placeholder {
  color: #9BA7BA !important;
}

.hide{
  display: none;
}

body {

  // 单行省略号
  .singleLineEllipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
    width: 100%;
  }

  // 多行省略号
  .multiLineEllipsis {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  // 通用字体
  .normalFont {
    color: #1B2532;
    font-feature-settings: 'clig' off, 'liga' off;
    /* 常规/14px */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
  }

  .boldFont {
    color: #1B2532;
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
  }

  // selector noBorderSelector
  .noBorderSelector.ant-select .ant-select-selector {
    border-radius: 4px;
    border-color: #FFF;
    border: 1px solid #FFF;
    background-color: rgba(255, 255, 255, 0.80);
  }

  // commonSelector
  .commonSelector {
    border: 0;
    background-color: transparent;

    div.ant-select-selector {
      border: 0 !important;
      background-color: transparent !important;
      box-shadow: none !important;

      .ant-select-selection-item {
        text-align: right;
      }
    }
  }
  .menu_dropdown .ant-dropdown-menu{
    height: 400px;
    overflow-y: auto;
  }

  .ant-dropdown {
    overflow-y: auto;

    .ant-dropdown-menu {
      box-shadow: none;
      padding: 2px;
    }
  }

  .ant-tree{
    min-width: 200px;
    max-height: 400px;
    overflow: auto;
  }
  .ant-tree-list{
    padding: 6px 10px;
  }
  .ant-tree-select-dropdown .ant-select-tree .ant-select-tree-node-content-wrapper.ant-select-tree-node-selected {
    outline: 0;
  }

  .ant-select-dropdown .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
    background-color: transparent;

    &::after {
      background: url('../images/commonList/common-select.svg');
      content: '';
      width: 18px;
      height: 18px;
      margin-top: 2px;
    }
  }

  .ant-select-dropdown .init-voice-option.ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
    &::after {
      margin-top: 10px;
    }
  }

  .ant-select-dropdown .ant-select-item-option-selected:not(.ant-select-item-option-disabled) .ant-select-item-option-state {
    display: none;
  }

  .knowledge-select-popup .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
    &::after {
      background: none !important;
      content: '' !important;
      width: 0 !important;
      height: 0 !important;
    }
  }

  // commonSpin
  .commonSpin {
    max-height: 700px !important;
  }

  .gantt-container {
    max-height: 500px !important;
  }

  // 对话模式
  // .chatModeBtn {
  //   .ant-select-selector {
  //     background: transparent !important;
  //     border-color: transparent !important;
  //   }
  // }
  /* 对话模式 */
.chatModeBtn {
  .ant-select-selector {
    width: 200px;                 
    border: none !important;      
    padding: 0;                   
    color: #a0a0a0;               
    background: transparent !important; 
    box-shadow: none !important;   
    transition: background-color 0.3s, border-color 0.3s, box-shadow 0.3s; 
  }
  .ant-select-selection-item{
    color: #1B2532;
  }

  /* Hover 状态时的样式 */
  .ant-select-selector:hover {
    background-color: #EBECF0 !important; 
    border-color: transparent !important; 
  }

  /* 焦点状态时，去除默认的蓝色边框 */
  .ant-select-focused:not(.ant-select-disabled).ant-select:not(.ant-select-customize-input) .ant-select-selector {
    border-color: transparent !important;  
    box-shadow: none !important;           
  }

  /* 焦点状态下，确保没有背景色和边框 */
  .ant-select-focused .ant-select-selector {
    background-color: transparent !important; 
    border-color: transparent !important;     
    box-shadow: none !important;            
  }

  /* 处理下拉箭头的样式 */
  .ant-select-arrow {
    transition: background-color 0.3s;
  }

  /* Hover 时修改箭头的背景颜色 */
  .ant-select-selector:hover .ant-select-arrow {
    background-color: #EBECF0 !important;
  }

  /* 焦点时修改箭头的背景颜色 */
  .ant-select-focused .ant-select-arrow {
    background-color: #EBECF0 !important;
  }

  /* 下拉框项 hover 和选中项的样式 */
  .ant-select-item-option:hover,
  .ant-select-item-option-selected {
    background-color: #EBECF0;
  }
}





  // input
  .ant-input-affix-wrapper {
    border-radius: 8px;
  }

  .inputSelector{
    .ant-select-dropdown .ant-select-item{
      padding: 0;
    }
  }
  .ant-input,
  .ant-input-number,
  .ant-select-single div.ant-select-selector {
    border-radius: 8px;
  }

  // .ant-input, .ant-select-multiple div.ant-select-selector{
  //   border-radius: 8px;
  // }
  .commonInput {
    border-radius: 4px;
    border: 1px solid #D5D7DE;
  }

  .noBorderInput input,
  .noBorderInput .ant-input-group .ant-input-group-addon {
    border: 0;

    img {
      margin-top: 4px;
      cursor: pointer;
    }
  }

  .noBorderInput {
    border: 0;
  }

  .transparentInput {
    border: 0px;
    border-color: transparent;
    background-color: transparent;
    padding: 0;
    color: #626F84;
  }

  .transparentInput:focus {
    box-shadow: none;
  }

  .zoomInput {
    width: 120px;
  }

  .error-inputs-tree-selector {
    .ant-select-selection-item {
      color: #ef0909 !important;
    }
  }
  .errorSelect{
    .ant-select-selection-item{
      color: #ef0909 !important;
    }
  }

  // commonSearchInput
  .commonSearchInput {
    width: 296px;
    border-radius: 4px;
    border: 1px solid #fff;
    height: 32px;
  }

  .commonSearchInput input {
    background: rgba(255, 255, 255, 0.80);
    border-left: 1px solid #fff;

    &:hover {
      border-left: 1px solid #fff;
    }

    &:focus {
      border-left: 1px solid #fff;
      outline: 0;
      box-shadow: none;
    }
  }

  .commonSearchInput .ant-input-group-addon {
    background: rgba(255, 255, 255, 0.80);
    border-radius: 0px;
  }

  .commonSearchInput .ant-input-group .ant-input-group-addon .ant-select {
    border-radius: 4px 0 0 4px;
    background: rgba(247, 249, 250, 0.80);

    .ant-select-selector {
      border: 0;
      border-radius: 4px 0 0 4px;
      border-right: 1px solid #fff;
    }
  }

  // titleTabs
  .titleTabs {
    padding: 4px;
    border-radius: 6px;
    border: 1px solid #FFF;
    // background: rgba(235, 240, 245, 0.40);
    background: #EBECF0;
    box-shadow: 0px 2px 4px 0px rgba(152, 141, 220, 0.22) inset;
    color: #626F84;
    height: 36px;
  }

  .tabItem {
    display: inline-block;
    padding: 2px 12px;
    cursor: pointer;
  }

  .activeTitleTab {
    color: #356DEE;
    border-radius: 4px;
    background: #fff;
  }

  // button
  button.defaultButton {
    height: 32px;
    line-height: 18px;
    padding: 5px 26px;
    justify-content: center;
    align-items: center;
    background: hsl(0, 0%, 100%);
    border-radius: 8px;
    border: 1px solid #D5D7DE;
    color: #626F84;

    img {
      display: inline-block;
      vertical-align: middle;
      margin-right: 8px;
    }

    &:hover {
      border: 1px solid #356DEE;
    }

    &.smallDefaultButton {
      padding: 5px 16px;
    }

    &.ant-btn-default:disabled:hover {
      border: 1px solid #d9d9d9;
    }
  }

  button.primaryButton {
    height: 32px;
    line-height: 18px;
    padding: 5px 26px;
    justify-content: center;
    align-items: center;
    border-radius: 8px;
    background: #1D7CFF;
    color: #fff;
    border: 1px solid transparent;
    box-shadow: none;

    img {
      display: inline-block;
      vertical-align: middle;
      margin-right: 8px;
    }

    &:hover {
      opacity: 0.8;
    }

    &:disabled {
      border: 1px solid transparent;
      ;
    }

    &.smallPrimaryButton {
      padding: 5px 16px;
    }

    &.ant-btn-primary:disabled:hover {
      opacity: 1;
    }
  }

  // commonTable
  .commonTable {
    position: relative;
    height: 100%;
    overflow-y: hidden;
  }

  .commonTable .ant-table {
    overflow-y: hidden;
    border-radius: 0px;
    height: calc(100% - 62px);
  }
  .commonTable .ant-table-header{
    // height: 48px;
    // line-height: 48px;
  }
  .commonTable .ant-table-thead>tr>th{
    padding: 9px 16px;
    background: #F7F9FA;
    color: #1B2532;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    height: 48px !important; /* 固定表头高度 */
    line-height: 48px; /* 垂直居中表头文本 */
  }

  .commonTable .ant-table-body {
    height: calc(100% - 41px);
    max-height: none;
  }
  .commonTable .ant-table-body > tr >td {
    height: 48px !important;
    line-height: 48px;
  }
  .commonTable.ant-table-wrapper .ant-table-container{
    height: 100%;
  }

  .commonTable .ant-table-container table>thead>tr:first-child>*:first-child {
    border-start-start-radius: 0px
  }

  .commonTable .ant-table-container table>thead>tr:first-child>*:last-child {
    border-start-end-radius: 0px
  }

  .commonTable .ant-table-tbody>tr>td {
    padding: 8px 16px;
  }

  .commonTable .ant-spin-nested-loading,
  .commonTable .ant-spin-nested-loading .ant-spin-container {
    height: 100%;
  }

  .flowSpinner .ant-spin-nested-loading {
    width: 100%;
    height: calc(100vh - 130px);
  }

  .commonTable .ant-table-pagination.ant-pagination {
    position: absolute;
    width: 100%;
    padding: 20px 16px;
    bottom: -9px;
    border-radius: 0 0 4px 4px;
    background: #fff;
    margin: 0 !important;
  }

  .logTable .ant-table-pagination.ant-pagination {
    position: relative;
  }

  // slider
  .commonSlider .ant-slider-track {
    background: linear-gradient(270deg, #1D7CFF 3.33%, #579DFF 100%), linear-gradient(90deg, #6B7CF8 8.23%, #3E74F7 98.56%);
    height: 4px;
    border-radius: 6px;
  }

  .commonSlider .ant-slider-rail {
    background: #D8E0E8;
  }

  .commonSlider .ant-slider-dot,
  .commonSlider .ant-slider-dot:first-child,
  .commonSlider .ant-slider-dot:last-child {
    display: none;
  }

  .commonSlider .ant-slider-mark-text:first-child {
    left: 0px !important;
  }

  .commonSlider .ant-slider-handle::after {
    box-shadow: 0 0 0 3px #1D7CFF
  }

  // monaco-editor
  .monaco-editor {
    border-radius: 4px;
  }

  .monaco-editor .overflow-guard {
    border-radius: 0 0 4px 4px
  }

  // common-progress
  .common-progress.ant-progress .ant-progress-bg {
    background: linear-gradient(270deg, rgb(29, 124, 255) 3.33%, rgb(87, 157, 255) 100%);
  }

  .common-progress.ant-progress .ant-progress-inner {
    background: #D5D7DE;
  }

  // ant-empty
  .common_ant_empty.ant-empty {
    padding: 140px 0 50px;
  }
  .block_ant_empty.ant-empty {
    padding: 100px 0 50px;
  }
  .block_ant_empty .ant-empty-image{
    height: 72px!important;
  }

  // ant-switch
  button.ant-switch {
    background-color: rgba(0, 0, 0, 0.25)
  }

  /* inputs_tabs */
  .inputs_tabs .ant-tabs-nav-list {
    width: calc(100% - 32px);
    margin-left: 16px;
  }

  .inputs_tabs.ant-tabs .ant-tabs-tab {
    width: 50%;
  }

  .inputs_tabs.ant-tabs>.ant-tabs-nav .ant-tabs-nav-wrap {
    overflow: visible;
  }

  .inputs_tabs.ant-tabs .ant-tabs-ink-bar {
    width: 50%;
    bottom: -1px;
    height: 1px;
  }

  .inputs_tabs.ant-tabs .ant-tabs-tab .ant-tabs-tab-btn {
    width: 100%;
    text-align: center;
  }

  .inputs_tabs.ant-tabs>.ant-tabs-nav {
    border-bottom: 1px solid #fff;
    width: 100%;

  }

  .inputs_tabs.ant-tabs .ant-tabs-tab+.ant-tabs-tab {
    margin: 0;
  }

  .inputs_tabs.ant-tabs .ant-tabs-content {
    color: #626F84;
  }

  // common_tabs
  .common_tabs.ant-tabs-top>.ant-tabs-nav::before {
    border-bottom: 0px;
  }

  .common_tabs.ant-tabs>.ant-tabs-nav .ant-tabs-nav-list {
    border-radius: 8px;
    background: rgba(237, 241, 245, 1);
    padding: 2px;
  }

  .common_tabs.ant-tabs .ant-tabs-tab-active {
    background: #fff;
    border-radius: 3px;
  }

  .common_tabs.ant-tabs-top>.ant-tabs-nav .ant-tabs-ink-bar {
    height: 0;
  }

  .common_tabs.ant-tabs .ant-tabs-tab-btn {
    padding: 0 24px;
  }

  .common_tabs.ant-tabs .ant-tabs-tab+.ant-tabs-tab {
    margin: 0;
  }

  .common_tabs.ant-tabs .ant-tabs-tab {
    padding: 4px 0;
    color: #626F84;
  }

  .common_tabs.ant-tabs-top>.ant-tabs-nav {
    margin: 0 0 4px 0;
  }
  .common_tabs.ant-tabs.common_tabs_nobg > .ant-tabs-nav .ant-tabs-nav-list{
    background: transparent;
  }
  .common_tabs.ant-tabs.common_tabs_nobg .ant-tabs-tab-active {
    background: #e8f5ff;
  }
  .common_tabs.ant-tabs.common_tabs_nobg .ant-tabs-tab-btn {
    padding: 0 4px;
    margin: 0 3px;
  }

  .block_inputs_tabs .ant-tabs-nav{
    padding: 0 16px 16px 16px;
    border-bottom: 1px solid #EDF1F5;
  }

  // full_tabs
  .full_tabs.ant-tabs>.ant-tabs-nav .ant-tabs-nav-list {
    width: 100%;
  }

  .full_tabs.ant-tabs .ant-tabs-tab {
    width: 50%;
  }

  .full_tabs.ant-tabs .ant-tabs-tab-btn {
    text-align: center;
    width: 100%;
  }

  // interactionTabs
  .interactionTabs .ant-tabs-nav {
    display: none;
  }

  // white_tabs
  .white_tabs.ant-tabs>.ant-tabs-nav .ant-tabs-nav-list {
    padding: 4px;
    background: #fff;
  }

  .white_tabs.ant-tabs .ant-tabs-tab-active {
    border-radius: 4px;
    background: #1D7CFF;
  }

  .white_tabs.ant-tabs .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
    color: #fff;
  }

  // common_card_tabs
  .common_card_tabs.ant-tabs>.ant-tabs-nav .ant-tabs-nav-list {
    transform: translate(16px, 0px) !important;
  }

  .common_card_tabs.ant-tabs-card.ant-tabs-top>.ant-tabs-nav .ant-tabs-tab+.ant-tabs-tab {
    margin-left: 8px;
  }

  .common_card_tabs.ant-tabs-card>.ant-tabs-nav .ant-tabs-tab {
    border-radius: 4px 4px 0 0;
    padding: 5px 16px;
  }

  .common_card_tabs.ant-tabs .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
    color: #006BFF;
    text-shadow: none;
  }

  .common_card_tabs .ant-tabs-tab-active .circleIcon {
    background: #1D7CFF;
  }

  // common_noBorder_tabs
  .common_noBorder_tabs {
    padding: 0 16px;
  }

  .common_noBorder_tabs.ant-tabs .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
    color: #006BFF;
    text-shadow: none;
  }

  .common_noBorder_tabs.ant-tabs .ant-tabs-tab-btn {
    font-size: 14px;
  }

  .common_noBorder_tabs.ant-tabs-top>.ant-tabs-nav::before {
    border-bottom: 0;
  }
  .common_tabs.ant-tabs .ant-tabs-tab-active {
    border-radius: 8px;
  }

  // commonModal
  .commonModal .ant-modal-header{
    border-radius: 8px 8px 0px 0px;
    border-bottom: 1px solid #EBF0F5;
    background: #F7F9FA;
    padding: 16px;
    margin-bottom: 0 !important;
    position: sticky;
    top: 0;
    z-index: 100; /* 确保表头在最上面 */
    
  }

  .commonModal.ant-modal {
    padding-bottom: 0;
  }

  .commonModal.ant-modal .ant-modal-content {
    padding: 0;
    background: #fff;
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .commonModal .ant-modal-body {
    padding: 24px 24px 12px 24px;
    overflow-y: auto; 
    max-height: 80vh; 
    flex-grow: 1; 
    position: relative;
    overflow-x: hidden;
  }
  .commonModal .ant-modal-body::-webkit-scrollbar {
    display: none; /* 隐藏滚动条 */
  }

  .commonModal .ant-modal-footer {

    border-top: 1px solid #EFF0F2;
    padding: 12px 24px 12px 16px;
    position: sticky;
    bottom: 0;
    background-color: #fff; /* 防止底部背景被滚动覆盖 */
    z-index: 100;
    border-radius: 0 0 8px 8px;
  }
  .commonModal.customModal .ant-modal-body {
    padding: 0px !important; /* 设置内容区域的 padding */
  }

  .commonModal.customModal .ant-modal-footer {
      margin-top: 0 !important; /* 设置底部的 margin-top */
  }

  .noFooterModal .ant-modal-footer {
    border-top: 0;
    padding: 0px;
  }

  .ant-modal {
    max-width: none;
  }
  
  // demoModal
  .demoModal {
    width: 880px !important;
  }

  .demoModal .ant-modal-body {
    padding: 0px;
  }

  .demoModal .ant-modal-header {
    margin-bottom: 0px;
  }

  // detailModal
  .detailModal.ant-modal .ant-modal-content {
    background-color: transparent;
  }

  .detailModal.ant-modal .ant-modal-header {
    background: transparent;
  }

  .detailModal.ant-modal .ant-modal-title {
    color: #1B2532;
  }

  .detailModal.ant-modal .ant-modal-title span,
  .ant-modal .ant-modal-title img {
    vertical-align: middle;
    display: inline-block;
    font-size: 12px;
  }

  .detailModal.ant-modal .ant-modal-title img {
    margin-right: 8px;
  }

  .detailModal.ant-modal .ant-modal-close {
    top: 20px;
    right: 20px;
  }

  // addItemModal
  .addItemModal.ant-modal {
    width: 910px !important;
  }

  .interaction-custom-modal .ant-modal-content {
    padding: 0;
  }

  .interaction-custom-modal .ant-modal-content .ant-modal-header{
    margin: 0;
  }

  .interaction-tabs {
    height: 100%;
    .ant-tabs-content-holder {
      width: 100%;
      overflow: auto;
    }
    .ant-tabs-nav-list {
      gap: 80px; /* 调整标签之间的间距，可以根据需要修改数值 */
    }
  }

  .interaction-custom-modal .ant-modal-content  .ant-modal-footer {
    margin: 0;
    border-radius: 0px 0px 4px 4px;
    border-top: 1px solid#EEEFF2;
    background: #FFF;
  }

  .interaction-segmented {
    .ant-segmented-item-selected {
      .ant-segmented-item-label {
        color:  #006BFF;
        font-feature-settings: 'liga' off, 'clig' off;
        /* 加粗/14px */
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
      }
    }
  }

  // popover
  div.ant-popover .ant-popover-inner {
    padding: 0;
  }

  div.ant-popover-inner-content .ant-popconfirm-inner-content {
    padding: 12px;
  }

  // 位置为正下方的popover
  div.ant-popover-placement-bottom.ant-popover .ant-popover-content {
    top: 16px
  }

  div.ant-popover-placement-leftTop.ant-popover .ant-popover-content{
    top: -5px;
    right: 2px;
  }

  /* json */
  div.jse-menu {
    display: none !important;
  }

  div.jse-text-mode {
    background: transparent !important;
    ;
  }

  div.jse-text-mode .jse-contents {
    border: 0 !important;
    border-top: 1px solid #fff !important;
  }

  div.jse-text-mode .jse-contents .cm-editor .cm-gutters {
    background: #fff !important;
    border-top: 0 !important;
    border-right: 0 !important;
  }

  div.jse-status-bar:last-child {
    border-radius: 0 0 4px 4px !important;
    background: #F7F8FA !important;
    padding: 3px 10px !important;
    border: 0 !important;
    border-top: 1px solid #fff !important;
    margin-bottom: 1px !important;
  }

  div.jse-status-bar .jse-status-bar-info {
    color: #626F84;
  }

  #jsoneditor-result {
    margin-top: 12px;
  }

  #jsoneditor-result .jse-status-bar {
    display: none;
  }

  div.jse-message.jse-error {
    display: none !important;
  }
  .cm-scroller{
    max-height: 480px;
  }

  /* mark down 编辑器 */
  div.vditor {
    border: 0;
  }
  div.vditor-toolbar {
    display: none;
  }

  div.vditor-ir pre.vditor-reset {
    border-radius: 4px;
  }

  .ant-message .ant-message-notice-wrapper .ant-message-error>.anticon {
    display: none
  }

  // 数字人 modal
  .digital-modal .ant-modal .ant-modal-content {
    border-radius: 4px;
    padding: 0;
    display: flex;
    flex-direction: column;
    gap: 24px;
    flex: 1 0 0;
  }

  .digital-modal .ant-modal-header {
    display: flex;
    padding: 16px 24px;
    justify-content: space-between;
    align-items: center;
    border-radius: 4px;
    border-bottom: 1px solid #EBECF0;
    background: #F7F8FA;
  }

  .digital-modal .ant-modal-body {
    padding: 0 24px;
  }

  .digital-modal .ant-modal-footer {
    display: flex;
    height: 56px;
    padding: 12px 24px;
    justify-content: flex-end;
    align-items: flex-end;
    align-self: stretch;
    border-radius: 4px;
    border-top: 1px solid #EBECF0;
    background: #FFF;
  }

  .digital-modal .ant-slider .ant-slider-track {
    background-color: #006BFF;
  }

  .digital-modal .ant-slider .ant-slider-handle::after {
    box-shadow: 0 0 0 2px #006BFF;
  }
}

.plus-flow-icon:hover,
.minus-flow-icon:hover {
  path {
    fill: #1D7CFF !important;
  }
}

.plus-flow-icon {
  margin-top: 16px;
  cursor: pointer;
  margin-left: 10px;
}

.minus-flow-icon {
  margin-top: 10px;
  cursor: pointer;
  margin-left: 10px;
}

.disabledFlowBtn {
  .plus-flow-icon {
    opacity: 0.4;
    cursor: not-allowed;

    &:hover {
      path {
        fill: #9BA7BA !important;
      }
    }
  }

  .minus-flow-icon {
    opacity: 0.4;
    cursor: not-allowed;

    &:hover {
      path {
        fill: #9BA7BA !important;
      }
    }
  }
}

.ant-collapse .ant-collapse-item-disabled>.ant-collapse-header {
  color: rgba(0, 0, 0, 0.88);
}
.input-collapse.ant-collapse>.ant-collapse-item >.ant-collapse-header{
  padding: 0px;
}
.input-collapse .ant-collapse-item > .ant-collapse-content > .ant-collapse-content-box{
  padding: 0;
}

.model-config-class .ant-collapse-item .ant-collapse-header {
  padding: 0;
  // border-radius: 4px !important;
}
.api-collapse .ant-collapse-item .ant-collapse-header {

  padding: 0;
}
.ant-collapse-item >.ant-collapse-content >.ant-collapse-content-box{
  padding: 0 16px;
}
.ant-collapse-ghost >.ant-collapse-item >.ant-collapse-content >.ant-collapse-content-box{
  padding-block: 0px;
}
.model-config-class .ant-collapse-item .ant-collapse-content .ant-collapse-content-box {
  padding: 0 16px 16px 16px;
}

.model-config-class .ant-collapse-item {
  margin-bottom: 16px;
  background-color: #E8F5FF;
  border-radius: 4px !important;
}

// interactionResize
.interactionResize div {
  cursor: default !important;
}

.bao-app {
  background: #fff !important;
}

// loading 效果, 单独的区域
.global-common-loading {
  position: absolute;
  width: 100%;
  height: 90%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 99;
  background-color: rgba(255, 255, 255, 0.50);
}
.ant-segmented .ant-segmented-item-selected {
  border-radius: 8px !important
}


.scroll-y {
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    // background: #fff;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #999999; // #BFC9D5;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #666666;  // #BFC9D5;
  }
}

.scroll-active-y {
  overflow-y: hidden;

  &:hover,
  &:active {
    overflow-y: auto;
  }

  &::-webkit-scrollbar {
    width: 6px;
    height: 12px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #555;
  }
}
// react-flow
// 9999todo
.react-flow__edges > svg:has(.loop-group-edge){
  z-index: 9999 !important;
}

.loop-group-edge1 g path{
  // stroke: red!important;
}
.loop-group-edge2 g path{
  // stroke: green!important;
}
.loop-group-edge3 g path{
  // stroke: purple!important;
}
.react-flow__controls-button{
  height: 32px!important;
}

.kGhPeq.linked .bg:not(.hasError){
  background: rgb(0, 107, 255);
}
.json-view-page {
 
  .w-json-view-container {
    max-height: 320px;
    overflow-y: auto;
    padding: 0 !important;
     > .w-rjv-wrap {
      padding-left: 0 !important;
      margin-left: 0 !important;
      padding: 3px 12px !important;
    }
    > span, > div:last-child {
        display: none !important;
    }
    .w-rjv-object-key {
      color: #006BFF !important;
    }
    .w-rjv-value, .w-rjv-quotes {
      color: #1D2531 !important;
    }
  }

  &.json-view-page-bgWrite {
      .w-json-view-container {
      > .w-rjv-wrap {
        padding: 0 !important;
      }
    }
  }
}

.ant-collapse>.ant-collapse-item >.ant-collapse-header .ant-collapse-arrow svg {
  color: #9BA7BA !important;
}

input::placeholder {
  color:  #9EA7B8 !important;
}
.ant-select-selection-placeholder{
  color:  #9EA7B8 !important;
}

textarea::-webkit-input-placeholder {
  color: #9BA7BA !important; //  #9BA7BA;
}

/* 聚焦时改变placeholder颜色 */
input:focus::placeholder {
  color: #9BA7BA !important;
}

// 运行，调试 loading
.runTestLoading {
    .ant-spin-text {
        color: #657083;
        font-size: 14px;
    }
}

.runFlowBtnTextArea {
   .ant-input {
      border: 0;
      box-shadow: unset;
      padding: 0;
    }
}
.nodeDetailContent {
  .ant-modal-close {
    right: 24px;
  }
}
.ant-input-affix-wrapper{
  transition:none;
}

[data-slate-editor="true"] {
	height: 100%;
	overflow: auto;
}

// markdown 预览
.vditor-reset {
  h1, h2, h3, h4, h5, h6 {
    &::before {
      content: "" !important;
    }
  }
}

// tailwind
@tailwind base;
@tailwind components;
@tailwind utilities;